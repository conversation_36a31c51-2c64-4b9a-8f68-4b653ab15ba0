import { useState } from 'react';
import { Edit, Lock, User, Mail, Building, Calendar } from 'lucide-react';
import PropTypes from 'prop-types';
import { ErrorAlert } from '../../common';
import { useLanguage } from '../../../hooks';
import ProfileEditModal from './ProfileEditModal';
import PasswordChangeModal from './PasswordChangeModal';

/**
 * Componente para gestión del perfil del usuario
 */
const ProfileManagement = ({
  currentUser,
  isLoading,
  error,
  onClearError,
  onUpdateProfile,
  onChangePassword,
  darkMode
}) => {
  const [showEditModal, setShowEditModal] = useState(false);
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const { t, currentLanguage } = useLanguage();

  const handleEditProfile = () => {
    setShowEditModal(true);
  };

  const handleUpdateProfile = async (profileData) => {
    await onUpdateProfile(profileData);
    setShowEditModal(false);
  };

  const handleChangePassword = async (passwordData) => {
    await onChangePassword(passwordData);
    setShowPasswordModal(false);
  };

  return (
    <>
      {/* Header elegante con gradiente */}
      <div className="mb-6 sm:mb-8">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 sm:gap-6">
          <div className="text-center sm:text-left">
            <h1 className="text-2xl sm:text-3xl font-light tracking-wide leading-relaxed mb-2 text-gray-900 dark:text-white">
              {t('profile.title')}
            </h1>
            <p className="text-sm sm:text-base text-gray-600 dark:text-gray-300 font-light tracking-wide">
              {t('profile.subtitle')}
            </p>
          </div>

          {/* Botones modernos con gradientes */}
          <div className="flex flex-col xs:flex-row gap-3 w-full sm:w-auto">
            <button
              onClick={() => setShowPasswordModal(true)}
              className="group flex items-center justify-center gap-2 sm:gap-3 px-4 sm:px-6 py-2.5 sm:py-3 rounded-xl font-light tracking-wide shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white text-sm sm:text-base"
            >
              <div className="p-1 bg-white/20 rounded-lg group-hover:bg-white/30 transition-all duration-300">
                <Lock size={16} />
              </div>
              <span>{t('profile.changePassword')}</span>
            </button>

            <button
              onClick={handleEditProfile}
              className="group flex items-center justify-center gap-2 sm:gap-3 px-4 sm:px-6 py-2.5 sm:py-3 rounded-xl font-light tracking-wide shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white text-sm sm:text-base"
            >
              <div className="p-1 bg-white/20 rounded-lg group-hover:bg-white/30 transition-all duration-300">
                <Edit size={16} />
              </div>
              <span>{t('profile.editProfile')}</span>
            </button>
          </div>
        </div>
      </div>

      <ErrorAlert error={error} onClose={onClearError} />

      {/* Tarjeta principal del perfil con diseño limpio */}
      <div className={`p-4 rounded-xl border shadow-sm ${
        darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
      }`}>
        <div className="p-4 sm:p-6 lg:p-8">
          {currentUser ? (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8">
              {/* Nombre Completo */}
              <div className="group">
                <div className="flex items-center gap-2 sm:gap-3 mb-3">
                  <User size={18} className="text-blue-500 sm:w-5 sm:h-5" />
                  <label className="text-xs sm:text-sm font-light tracking-wide text-gray-600 dark:text-gray-400">
                    {t('profile.fullName')}
                  </label>
                </div>
                <div className={`p-3 sm:p-4 rounded-xl border-2 transition-all duration-300 group-hover:border-blue-300 dark:group-hover:border-blue-600 ${
                  darkMode
                    ? 'bg-gray-900/50 border-gray-600'
                    : 'bg-gray-50 border-gray-200'
                }`}>
                  <p className="text-base sm:text-lg font-light tracking-wide text-gray-900 dark:text-white">
                    {currentUser.firstName} {currentUser.lastName}
                  </p>
                </div>
              </div>

              {/* Email */}
              <div className="group">
                <div className="flex items-center gap-2 sm:gap-3 mb-3">
                  <Mail size={18} className="text-green-500 sm:w-5 sm:h-5" />
                  <label className="text-xs sm:text-sm font-light tracking-wide text-gray-600 dark:text-gray-400">
                    {t('profile.email')}
                  </label>
                </div>
                <div className={`p-3 sm:p-4 rounded-xl border-2 transition-all duration-300 group-hover:border-green-300 dark:group-hover:border-green-600 ${
                  darkMode
                    ? 'bg-gray-900/50 border-gray-600'
                    : 'bg-gray-50 border-gray-200'
                }`}>
                  <p className="text-sm sm:text-lg font-light tracking-wide text-gray-900 dark:text-white break-all">
                    {currentUser.email}
                  </p>
                </div>
              </div>

              {/* Empresa */}
              <div className="group">
                <div className="flex items-center gap-2 sm:gap-3 mb-3">
                  <Building size={18} className="text-purple-500 sm:w-5 sm:h-5" />
                  <label className="text-xs sm:text-sm font-light tracking-wide text-gray-600 dark:text-gray-400">
                    {t('profile.company')}
                  </label>
                </div>
                <div className={`p-3 sm:p-4 rounded-xl border-2 transition-all duration-300 group-hover:border-purple-300 dark:group-hover:border-purple-600 ${
                  darkMode
                    ? 'bg-gray-900/50 border-gray-600'
                    : 'bg-gray-50 border-gray-200'
                }`}>
                  <p className="text-base sm:text-lg font-light tracking-wide text-gray-900 dark:text-white">
                    {currentUser.company || t('profile.notSpecified')}
                  </p>
                </div>
              </div>

              {/* Fecha de Registro */}
              <div className="group">
                <div className="flex items-center gap-2 sm:gap-3 mb-3">
                  <Calendar size={18} className="text-orange-500 sm:w-5 sm:h-5" />
                  <label className="text-xs sm:text-sm font-light tracking-wide text-gray-600 dark:text-gray-400">
                    {t('profile.registrationDate')}
                  </label>
                </div>
                <div className={`p-3 sm:p-4 rounded-xl border-2 transition-all duration-300 group-hover:border-orange-300 dark:group-hover:border-orange-600 ${
                  darkMode
                    ? 'bg-gray-900/50 border-gray-600'
                    : 'bg-gray-50 border-gray-200'
                }`}>
                  <p className="text-base sm:text-lg font-light tracking-wide text-gray-900 dark:text-white">
                    {currentUser.createdAt ? new Date(currentUser.createdAt).toLocaleDateString(
                      currentLanguage === 'es' ? 'es-ES' : 'en-US', {
                      day: '2-digit',
                      month: '2-digit',
                      year: 'numeric'
                    }) : 'N/A'}
                  </p>
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="inline-flex items-center gap-3 px-6 py-3 rounded-xl bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-200 dark:border-blue-700">
                <div className="w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                <span className="text-gray-600 dark:text-gray-300 font-light tracking-wide">
                  Cargando información del perfil...
                </span>
              </div>
            </div>
          )}
        </div>
      </div>

      <ProfileEditModal
        isOpen={showEditModal}
        currentUser={currentUser}
        onClose={() => setShowEditModal(false)}
        onUpdate={handleUpdateProfile}
        darkMode={darkMode}
      />

      <PasswordChangeModal
        isOpen={showPasswordModal}
        onClose={() => setShowPasswordModal(false)}
        onChangePassword={handleChangePassword}
        darkMode={darkMode}
      />
    </>
  );
};

ProfileManagement.propTypes = {
  currentUser: PropTypes.object,
  isLoading: PropTypes.bool.isRequired,
  error: PropTypes.string,
  onClearError: PropTypes.func.isRequired,
  onUpdateProfile: PropTypes.func.isRequired,
  onChangePassword: PropTypes.func.isRequired,
  darkMode: PropTypes.bool.isRequired
};

export default ProfileManagement;
