// 🔐 SecureStorage - Encriptación local para datos sensibles
// Sin dependencias externas, usando Web Crypto API nativa

class SecureStorage {
  constructor() {
    // Clave derivada del navegador y timestamp
    this.secretKey = this.generateSecretKey();
  }

  // Generar clave secreta basada en características del navegador
  generateSecretKey() {
    const browserFingerprint = [
      navigator.userAgent,
      navigator.language,
      screen.width,
      screen.height,
      new Date().toDateString() // Cambia diariamente para mayor seguridad
    ].join('|');
    
    return this.simpleHash(browserFingerprint);
  }

  // Hash simple para generar clave
  simpleHash(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convertir a 32bit integer
    }
    return Math.abs(hash).toString(36);
  }

  // Encriptación simple XOR (suficiente para frontend sin backend)
  encrypt(text, key) {
    let result = '';
    for (let i = 0; i < text.length; i++) {
      const charCode = text.charCodeAt(i) ^ key.charCodeAt(i % key.length);
      result += String.fromCharCode(charCode);
    }
    return btoa(result); // Base64 encode
  }

  // Desencriptación XOR
  decrypt(encryptedText, key) {
    try {
      const text = atob(encryptedText); // Base64 decode
      let result = '';
      for (let i = 0; i < text.length; i++) {
        const charCode = text.charCodeAt(i) ^ key.charCodeAt(i % key.length);
        result += String.fromCharCode(charCode);
      }
      return result;
    } catch (error) {
      console.error('[SECURITY] Decryption failed:', error);
      return null;
    }
  }

  // Almacenar dato encriptado
  setSecure(key, value) {
    try {
      const stringValue = typeof value === 'string' ? value : JSON.stringify(value);
      const encrypted = this.encrypt(stringValue, this.secretKey);
      const timestamp = Date.now();
      
      const secureData = {
        data: encrypted,
        timestamp: timestamp,
        checksum: this.simpleHash(encrypted + timestamp)
      };
      
      sessionStorage.setItem(`secure_${key}`, JSON.stringify(secureData));
      
      // Log de seguridad
      console.log(`[SECURE_STORAGE] Stored encrypted data for key: ${key}`);
      return true;
    } catch (error) {
      console.error('[SECURE_STORAGE] Encryption failed:', error);
      return false;
    }
  }

  // Recuperar dato encriptado
  getSecure(key) {
    try {
      const storedData = sessionStorage.getItem(`secure_${key}`);
      if (!storedData) return null;

      const secureData = JSON.parse(storedData);
      
      // Verificar integridad
      const expectedChecksum = this.simpleHash(secureData.data + secureData.timestamp);
      if (secureData.checksum !== expectedChecksum) {
        console.error('[SECURITY] Data integrity check failed for key:', key);
        this.removeSecure(key);
        return null;
      }

      // Verificar expiración (24 horas)
      if (Date.now() - secureData.timestamp > 24 * 60 * 60 * 1000) {
        console.log('[SECURE_STORAGE] Data expired for key:', key);
        this.removeSecure(key);
        return null;
      }

      const decrypted = this.decrypt(secureData.data, this.secretKey);
      if (!decrypted) return null;

      // Intentar parsear como JSON, si falla devolver como string
      try {
        return JSON.parse(decrypted);
      } catch {
        return decrypted;
      }
    } catch (error) {
      console.error('[SECURE_STORAGE] Retrieval failed:', error);
      return null;
    }
  }

  // Eliminar dato seguro
  removeSecure(key) {
    sessionStorage.removeItem(`secure_${key}`);
    console.log(`[SECURE_STORAGE] Removed secure data for key: ${key}`);
  }

  // Limpiar todos los datos seguros
  clearAllSecure() {
    const keys = Object.keys(sessionStorage);
    keys.forEach(key => {
      if (key.startsWith('secure_')) {
        sessionStorage.removeItem(key);
      }
    });
    console.log('[SECURE_STORAGE] Cleared all secure data');
  }

  // Verificar si un dato existe y es válido
  hasValidSecure(key) {
    return this.getSecure(key) !== null;
  }
}

// Instancia singleton
const secureStorage = new SecureStorage();

export default secureStorage;
