import { useRef, useEffect, useState } from 'react';
import gsap from 'gsap';
import PropTypes from 'prop-types';
import { Menu, X } from 'lucide-react';
import LanguageSelector from './LanguageSelector';
import Sidebar from './Sidebar';

/**
 * Componente MainContent reutilizable
 * Proporciona el contenedor principal con animaciones consistentes
 */
const MainContent = ({
  children,
  darkMode,
  className = "",
  // Props para móvil
  title,
  currentUser,
  onToggleDarkMode,
  onLogout,
  navigationItems,
  activeSection,
  onSectionChange
}) => {
  const mainContentRef = useRef(null);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  // Animaciones GSAP al montar
  useEffect(() => {
    gsap.fromTo(
      mainContentRef.current,
      { y: 100, opacity: 0 },
      { y: 0, opacity: 1, duration: 1, ease: "power2.out", delay: 0.3 }
    );
  }, []);

  return (
    <>
      <main
        ref={mainContentRef}
        className={`flex-1 p-3 sm:p-6 lg:p-10 overflow-y-auto relative transition-colors duration-500
          ${darkMode ? "bg-gray-700" : "bg-gray-50"} ${className}`}
      >
        {/* Header móvil con menú hamburguesa */}
        <div className="flex justify-between items-center mb-4 sm:mb-6 lg:hidden">
          <button
            onClick={() => setIsMobileMenuOpen(true)}
            className={`p-2 rounded-lg transition-colors ${
              darkMode
                ? 'hover:bg-gray-600 text-white'
                : 'hover:bg-gray-200 text-gray-900'
            }`}
          >
            <Menu size={24} />
          </button>
          <LanguageSelector darkMode={darkMode} />
        </div>

        {/* Header desktop con selector de idioma */}
        <div className="hidden lg:flex justify-end mb-6">
          <LanguageSelector darkMode={darkMode} />
        </div>

        {/* Contenedor principal */}
        <div
          className={`relative z-10 transition-all duration-500 rounded-xl p-4 sm:p-6 lg:p-8 shadow-md border
            ${darkMode
              ? "bg-gray-800 border-gray-700 text-white"
              : "bg-white border-gray-200 text-gray-900"}`}
        >
          {children}
        </div>
      </main>

      {/* Sidebar móvil como overlay */}
      {isMobileMenuOpen && (
        <div className="lg:hidden fixed inset-0 z-50 flex">
          {/* Overlay */}
          <div
            className="fixed inset-0 bg-black bg-opacity-50"
            onClick={() => setIsMobileMenuOpen(false)}
          />

          {/* Sidebar */}
          <div className="relative w-80 max-w-[85vw]">
            <Sidebar
              title={title}
              currentUser={currentUser}
              darkMode={darkMode}
              onToggleDarkMode={onToggleDarkMode}
              onLogout={onLogout}
              navigationItems={navigationItems}
              activeSection={activeSection}
              onSectionChange={(section) => {
                onSectionChange(section);
                setIsMobileMenuOpen(false);
              }}
            />

            {/* Botón cerrar móvil */}
            <button
              onClick={() => setIsMobileMenuOpen(false)}
              className="absolute top-4 right-4 p-2 rounded-lg bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
            >
              <X size={20} className="text-gray-600 dark:text-gray-300" />
            </button>
          </div>
        </div>
      )}
    </>
  );
};

MainContent.propTypes = {
  children: PropTypes.node.isRequired,
  darkMode: PropTypes.bool.isRequired,
  className: PropTypes.string,
  // Props opcionales para móvil
  title: PropTypes.string,
  currentUser: PropTypes.object,
  onToggleDarkMode: PropTypes.func,
  onLogout: PropTypes.func,
  navigationItems: PropTypes.array,
  activeSection: PropTypes.string,
  onSectionChange: PropTypes.func
};

export default MainContent;
