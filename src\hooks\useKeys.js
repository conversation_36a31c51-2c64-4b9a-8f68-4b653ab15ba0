/**
 * useKeys Hook
 * Hook personalizado para manejo de llaves de encriptación
 */

import { useState, useCallback } from 'react';
import { keyService } from '../services/index.js';

export const useKeys = () => {
  const [keys, setKeys] = useState([]);
  const [statistics, setStatistics] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  /**
   * Subir llave a CTM
   */
  const uploadToCTM = useCallback(async (keyData) => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await keyService.uploadToCTM(keyData);

      // Preservar el algoritmo original del frontend si el backend no lo devuelve correctamente
      const enhancedResponse = {
        ...response,
        algorithm: keyData.algorithm || response.algorithm,
        // Asegurar que el algoritmo esté disponible en ambos campos
        type: keyData.algorithm || response.algorithm || response.type
      };

      // Agregar nueva llave a la lista (siempre)
      setKeys(prevKeys => [enhancedResponse, ...prevKeys]);

      // Actualizar estadísticas
      setStatistics(prevStats => ({
        ...prevStats,
        total: (prevStats?.total || 0) + 1,
        successful: (prevStats?.successful || 0) + 1,
        uploadedToCtm: enhancedResponse.uploadedToCtm ? (prevStats?.uploadedToCtm || 0) + 1 : (prevStats?.uploadedToCtm || 0)
      }));

      return enhancedResponse;
    } catch (error) {
      setError(error.message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Obtener llaves por usuario
   */
  const getKeysByUser = useCallback(async (userId, filters = {}) => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await keyService.getKeysByUser(userId, filters);

      // Procesar las llaves para asegurar que tengan el algoritmo correcto
      const processedKeys = response.keys.map(key => {
        // Si el tipo es 'hex_key' pero no hay algoritmo, intentar inferirlo o usar un valor por defecto
        if (key.type === 'hex_key' && !key.algorithm) {
          // Intentar inferir el algoritmo basado en el tamaño de bytes
          let inferredAlgorithm = 'AES'; // Por defecto
          if (key.num_bytes <= 32) {
            inferredAlgorithm = 'AES';
          } else if (key.num_bytes <= 64) {
            inferredAlgorithm = 'HMAC';
          } else if (key.num_bytes <= 512) {
            inferredAlgorithm = 'RSA';
          }

          return {
            ...key,
            algorithm: inferredAlgorithm,
            type: inferredAlgorithm
          };
        }

        // Si ya tiene algoritmo, asegurar que esté en ambos campos
        return {
          ...key,
          algorithm: key.algorithm || key.type,
          type: key.algorithm || key.type
        };
      });

      // Si es la primera página, reemplazar; si no, agregar
      if (!filters.page || filters.page === 1) {
        setKeys(processedKeys);
      } else {
        setKeys(prevKeys => [...prevKeys, ...processedKeys]);
      }

      // Actualizar estadísticas
      setStatistics({
        total: response.total,
        successful: response.successful,
        failed: response.failed,
        uploadedToCtm: response.uploadedToCtm,
      });

      return { ...response, keys: processedKeys };
    } catch (error) {
      setError(error.message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Obtener estadísticas de llaves
   */
  const getStatistics = useCallback(async (userId) => {
    try {
      setError(null);
      
      const response = await keyService.getKeyStatistics(userId);
      setStatistics(response);
      
      return response;
    } catch (error) {
      setError(error.message);
      throw error;
    }
  }, []);

  /**
   * Limpiar errores
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  /**
   * Limpiar llaves
   */
  const clearKeys = useCallback(() => {
    setKeys([]);
    setStatistics(null);
  }, []);

  /**
   * Filtrar llaves localmente
   */
  const filterKeys = useCallback((filters) => {
    let filteredKeys = [...keys];
    
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      filteredKeys = filteredKeys.filter(key => 
        key.name.toLowerCase().includes(searchTerm) ||
        key.displayName.toLowerCase().includes(searchTerm)
      );
    }
    
    if (filters.algorithm) {
      filteredKeys = filteredKeys.filter(key => key.algorithm === filters.algorithm);
    }
    
    if (filters.status) {
      filteredKeys = filteredKeys.filter(key => key.status === filters.status);
    }
    
    if (filters.uploadedToCtm !== undefined) {
      filteredKeys = filteredKeys.filter(key => key.uploadedToCtm === filters.uploadedToCtm);
    }
    
    return filteredKeys;
  }, [keys]);

  /**
   * Eliminar una llave
   */
  const deleteKey = useCallback(async (keyId) => {
    try {
      setIsLoading(true);
      setError(null);

      await keyService.deleteKey(keyId);

      // Actualizar la lista de llaves removiendo la llave eliminada
      setKeys(prevKeys => prevKeys.filter(key => key.id !== keyId));

      // Actualizar estadísticas
      setStatistics(prevStats => ({
        ...prevStats,
        total: Math.max(0, (prevStats?.total || 0) - 1)
      }));

      return true;
    } catch (error) {
      setError(error.message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  return {
    // Estado
    keys,
    statistics,
    isLoading,
    error,

    // Acciones
    uploadToCTM,
    getKeysByUser,
    getStatistics,
    deleteKey,
    clearError,
    clearKeys,
    filterKeys,
  };
};

/**
 * useKeyUpload Hook
 * Hook específico para subida de llaves
 */
export const useKeyUpload = () => {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [error, setError] = useState(null);
  const [result, setResult] = useState(null);

  /**
   * Subir llave con progreso
   */
  const uploadKey = useCallback(async (keyData) => {
    try {
      setIsUploading(true);
      setError(null);
      setResult(null);
      setUploadProgress(0);

      // Simular progreso
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 200);

      const response = await keyService.uploadToCTM(keyData);
      
      clearInterval(progressInterval);
      setUploadProgress(100);
      setResult(response);
      
      return response;
    } catch (error) {
      setError(error.message);
      setUploadProgress(0);
      throw error;
    } finally {
      setIsUploading(false);
    }
  }, []);

  /**
   * Resetear estado
   */
  const reset = useCallback(() => {
    setIsUploading(false);
    setUploadProgress(0);
    setError(null);
    setResult(null);
  }, []);

  return {
    // Estado
    isUploading,
    uploadProgress,
    error,
    result,
    
    // Acciones
    uploadKey,
    reset,
  };
};
