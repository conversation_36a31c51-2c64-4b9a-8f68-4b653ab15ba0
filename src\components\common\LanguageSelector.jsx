import { useState, useRef, useEffect } from 'react';
import { Globe, ChevronDown } from 'lucide-react';
import PropTypes from 'prop-types';
import { useLanguage } from '../../hooks/useLanguage';

/**
 * Componente selector de idioma con diseño moderno y consistente
 */
const LanguageSelector = ({ darkMode, className = "" }) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);
  const { languages, currentLanguage, getCurrentLanguageInfo, changeLanguage, t } = useLanguage();

  const currentLangInfo = getCurrentLanguageInfo();

  // Cerrar dropdown al hacer clic fuera
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleLanguageChange = async (languageCode) => {
    const success = await changeLanguage(languageCode);
    if (success) {
      setIsOpen(false);
    }
  };

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* Botón principal */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`group flex items-center gap-3 px-4 py-3 rounded-xl border shadow-sm transition-all duration-300 transform hover:scale-105 hover:shadow-lg ${
          darkMode
            ? 'bg-gray-800 border-gray-600 hover:border-gray-500 text-white'
            : 'bg-white border-gray-200 hover:border-gray-300 text-gray-900'
        }`}
        title={t('language.selector')}
      >
        {/* Icono de globo */}
        <div className={`p-1.5 rounded-lg transition-all duration-300 ${
          darkMode
            ? 'bg-blue-600 group-hover:bg-blue-500'
            : 'bg-blue-500 group-hover:bg-blue-600'
        }`}>
          <Globe size={16} className="text-white" />
        </div>

        {/* Bandera e idioma actual */}
        <div className="flex items-center gap-2">
          <span className="text-lg">{currentLangInfo.flag}</span>
          <span className="font-light tracking-wide text-sm hidden sm:block">
            {currentLangInfo.nativeName}
          </span>
        </div>

        {/* Icono de flecha */}
        <ChevronDown 
          size={16} 
          className={`transition-transform duration-300 ${isOpen ? 'rotate-180' : ''}`}
        />
      </button>

      {/* Dropdown */}
      {isOpen && (
        <div className={`absolute top-full right-0 mt-2 w-48 rounded-xl border shadow-xl z-50 overflow-hidden transition-all duration-300 ${
          darkMode
            ? 'bg-gray-800 border-gray-600'
            : 'bg-white border-gray-200'
        }`}>
          {/* Header del dropdown */}
          <div className={`px-4 py-3 border-b ${
            darkMode ? 'border-gray-600 bg-gray-700' : 'border-gray-200 bg-gray-50'
          }`}>
            <div className="flex items-center gap-2">
              <Globe size={16} className={darkMode ? 'text-blue-400' : 'text-blue-500'} />
              <span className="text-sm font-light tracking-wide text-gray-600 dark:text-gray-300">
                {t('language.selector')}
              </span>
            </div>
          </div>

          {/* Lista de idiomas */}
          <div className="py-2">
            {languages.map((language) => (
              <button
                key={language.code}
                onClick={() => handleLanguageChange(language.code)}
                className={`w-full flex items-center gap-3 px-4 py-3 transition-all duration-200 hover:bg-opacity-80 ${
                  currentLanguage === language.code
                    ? darkMode
                      ? 'bg-blue-600/20 text-blue-400'
                      : 'bg-blue-50 text-blue-600'
                    : darkMode
                      ? 'text-gray-300 hover:bg-gray-700'
                      : 'text-gray-700 hover:bg-gray-50'
                }`}
              >
                {/* Bandera */}
                <span className="text-lg">{language.flag}</span>
                
                {/* Nombre del idioma */}
                <div className="flex-1 text-left">
                  <span className="font-light tracking-wide">
                    {language.nativeName}
                  </span>
                  {currentLanguage === language.code && (
                    <div className="flex items-center gap-1 mt-1">
                      <div className={`w-2 h-2 rounded-full ${
                        darkMode ? 'bg-blue-400' : 'bg-blue-500'
                      }`}></div>
                      <span className="text-xs font-light tracking-wide opacity-75">
                        Actual
                      </span>
                    </div>
                  )}
                </div>

                {/* Indicador de selección */}
                {currentLanguage === language.code && (
                  <div className={`w-2 h-2 rounded-full ${
                    darkMode ? 'bg-blue-400' : 'bg-blue-500'
                  } animate-pulse`}></div>
                )}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

LanguageSelector.propTypes = {
  darkMode: PropTypes.bool.isRequired,
  className: PropTypes.string
};

export default LanguageSelector;
