import { useState } from 'react';
import { Lock, Eye, EyeOff } from 'lucide-react';
import PropTypes from 'prop-types';
import { Modal, Button, FormField, ErrorAlert } from '../../common';
import { useLanguage } from '../../../hooks';
import { validationRules } from '../../../utils/validations';

/**
 * Modal para cambio de contraseña del usuario
 */
const PasswordChangeModal = ({ isOpen, onClose, onChangePassword, darkMode }) => {
  const [formData, setFormData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });
  const [errors, setErrors] = useState({});
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false
  });
  const [isChanging, setIsChanging] = useState(false);
  const [error, setError] = useState(null);
  const { t } = useLanguage();

  const handleChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Limpiar error del campo cuando el usuario empiece a escribir
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }
    
    // Limpiar error general
    if (error) {
      setError(null);
    }
  };

  const togglePasswordVisibility = (field) => {
    setShowPasswords(prev => ({ ...prev, [field]: !prev[field] }));
  };

  const validateForm = () => {
    const newErrors = {};

    // Validar contraseña actual
    if (!formData.currentPassword) {
      newErrors.currentPassword = t('profile.changePasswordModal.errors.currentRequired');
    }

    // Validar nueva contraseña
    const passwordError = validationRules.password(formData.newPassword);
    if (passwordError) {
      newErrors.newPassword = passwordError;
    }

    // Validar confirmación de contraseña
    if (!formData.confirmPassword) {
      newErrors.confirmPassword = t('profile.changePasswordModal.errors.confirmRequired');
    } else if (formData.newPassword !== formData.confirmPassword) {
      newErrors.confirmPassword = t('profile.changePasswordModal.errors.passwordsNotMatch');
    }

    // Validar que la nueva contraseña sea diferente a la actual
    if (formData.currentPassword && formData.newPassword &&
        formData.currentPassword === formData.newPassword) {
      newErrors.newPassword = t('profile.changePasswordModal.errors.samePassword');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsChanging(true);
    setError(null);

    try {
      await onChangePassword({
        currentPassword: formData.currentPassword,
        newPassword: formData.newPassword
      });
      
      // Reset form
      setFormData({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      });
      setErrors({});
      
      // Cerrar modal
      onClose();
    } catch (err) {
      setError(err.message || 'Error al cambiar la contraseña');
    } finally {
      setIsChanging(false);
    }
  };

  const handleClose = () => {
    setFormData({
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    });
    setErrors({});
    setError(null);
    setShowPasswords({
      current: false,
      new: false,
      confirm: false
    });
    onClose();
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title={
        <div className="flex items-center gap-3">
          <Lock size={24} className="text-blue-500" />
          <div>
            <h3 className="text-2xl font-light tracking-wide leading-relaxed text-gray-900 dark:text-white">
              {t('profile.changePasswordModal.title')}
            </h3>
            <p className="text-sm font-light tracking-wide text-gray-600 dark:text-gray-400">
              {t('profile.changePasswordModal.subtitle')}
            </p>
          </div>
        </div>
      }
      maxWidth="max-w-lg"
      darkMode={darkMode}
    >

      <form onSubmit={handleSubmit} className="space-y-6 px-4 pb-6">
        <ErrorAlert error={error} onClose={() => setError(null)} />

        {/* Contraseña actual */}
        <div className="group">
          <div className="flex items-center gap-3 mb-3">
            <Lock size={18} className="text-gray-500" />
            <label className="text-sm font-light tracking-wide text-gray-600 dark:text-gray-400">
              {t('profile.changePasswordModal.currentPassword')}
            </label>
          </div>
          <div className="relative">
            <input
              type={showPasswords.current ? "text" : "password"}
              value={formData.currentPassword}
              onChange={(e) => handleChange('currentPassword', e.target.value)}
              required
              disabled={isChanging}
              className={`w-full px-4 py-4 pr-12 rounded-xl border-2 font-light tracking-wide transition-all duration-300 focus:ring-4 focus:ring-blue-100 focus:border-blue-500 ${
                darkMode
                  ? 'bg-gray-900/50 border-gray-600 text-white placeholder-gray-400'
                  : 'bg-gray-50 border-gray-200 text-gray-900 placeholder-gray-500'
              } ${isChanging ? 'opacity-50 cursor-not-allowed' : ''}`}
              placeholder={t('profile.changePasswordModal.currentPasswordPlaceholder')}
            />
            <button
              type="button"
              onClick={() => togglePasswordVisibility('current')}
              className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-blue-500 dark:text-gray-400 dark:hover:text-blue-400 transition-colors duration-200"
              disabled={isChanging}
            >
              {showPasswords.current ? <EyeOff size={20} /> : <Eye size={20} />}
            </button>
          </div>
          {errors.currentPassword && (
            <p className="text-red-500 text-sm mt-2 font-light">{errors.currentPassword}</p>
          )}
        </div>

        {/* Nueva contraseña */}
        <div className="group">
          <div className="flex items-center gap-3 mb-3">
            <Lock size={18} className="text-green-500" />
            <label className="text-sm font-light tracking-wide text-gray-600 dark:text-gray-400">
              {t('profile.changePasswordModal.newPassword')}
            </label>
          </div>
          <div className="relative">
            <input
              type={showPasswords.new ? "text" : "password"}
              value={formData.newPassword}
              onChange={(e) => handleChange('newPassword', e.target.value)}
              required
              disabled={isChanging}
              className={`w-full px-4 py-4 pr-12 rounded-xl border-2 font-light tracking-wide transition-all duration-300 focus:ring-4 focus:ring-green-100 focus:border-green-500 ${
                darkMode
                  ? 'bg-gray-900/50 border-gray-600 text-white placeholder-gray-400'
                  : 'bg-gray-50 border-gray-200 text-gray-900 placeholder-gray-500'
              } ${isChanging ? 'opacity-50 cursor-not-allowed' : ''}`}
              placeholder={t('profile.changePasswordModal.newPasswordPlaceholder')}
            />
            <button
              type="button"
              onClick={() => togglePasswordVisibility('new')}
              className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-blue-500 dark:text-gray-400 dark:hover:text-blue-400 transition-colors duration-200"
              disabled={isChanging}
            >
              {showPasswords.new ? <EyeOff size={20} /> : <Eye size={20} />}
            </button>
          </div>
          {errors.newPassword && (
            <p className="text-red-500 text-sm mt-2 font-light">{errors.newPassword}</p>
          )}
        </div>

        {/* Confirmar nueva contraseña */}
        <div className="group">
          <div className="flex items-center gap-3 mb-3">
            <Lock size={18} className="text-blue-500" />
            <label className="text-sm font-light tracking-wide text-gray-600 dark:text-gray-400">
              {t('profile.changePasswordModal.confirmPassword')}
            </label>
          </div>
          <div className="relative">
            <input
              type={showPasswords.confirm ? "text" : "password"}
              value={formData.confirmPassword}
              onChange={(e) => handleChange('confirmPassword', e.target.value)}
              required
              disabled={isChanging}
              className={`w-full px-4 py-4 pr-12 rounded-xl border-2 font-light tracking-wide transition-all duration-300 focus:ring-4 focus:ring-blue-100 focus:border-blue-500 ${
                darkMode
                  ? 'bg-gray-900/50 border-gray-600 text-white placeholder-gray-400'
                  : 'bg-gray-50 border-gray-200 text-gray-900 placeholder-gray-500'
              } ${isChanging ? 'opacity-50 cursor-not-allowed' : ''}`}
              placeholder={t('profile.changePasswordModal.confirmPasswordPlaceholder')}
            />
            <button
              type="button"
              onClick={() => togglePasswordVisibility('confirm')}
              className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-blue-500 dark:text-gray-400 dark:hover:text-blue-400 transition-colors duration-200"
              disabled={isChanging}
            >
              {showPasswords.confirm ? <EyeOff size={20} /> : <Eye size={20} />}
            </button>
          </div>
          {errors.confirmPassword && (
            <p className="text-red-500 text-sm mt-2 font-light">{errors.confirmPassword}</p>
          )}
        </div>

        {/* Información sobre requisitos de contraseña */}
        <div className={`p-4 rounded-lg border ${
          darkMode ? 'bg-blue-900/20 border-blue-700' : 'bg-blue-50 border-blue-200'
        }`}>
          <h5 className="font-medium text-blue-800 dark:text-blue-300 mb-2">
            📋 {t('profile.changePasswordModal.requirements')}
          </h5>
          <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
            <li>• {t('profile.changePasswordModal.requirementsList.minLength')}</li>
            <li>• {t('profile.changePasswordModal.requirementsList.alphanumeric')}</li>
            <li>• {t('profile.changePasswordModal.requirementsList.different')}</li>
          </ul>
        </div>

        {/* Botones modernos */}
        <div className="flex flex-col sm:flex-row gap-3 pt-6">
          <button
            type="button"
            onClick={handleClose}
            disabled={isChanging}
            className={`px-8 py-3 rounded-xl font-light tracking-wide transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl ${
              darkMode
                ? 'bg-gray-700 hover:bg-gray-600 text-white border border-gray-600'
                : 'bg-gray-100 hover:bg-gray-200 text-gray-700 border border-gray-300'
            } ${isChanging ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            {t('common.cancel')}
          </button>
          <button
            type="submit"
            disabled={!formData.currentPassword || !formData.newPassword || !formData.confirmPassword || isChanging}
            className={`px-8 py-3 rounded-xl font-light tracking-wide transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white flex items-center justify-center gap-2 ${
              (!formData.currentPassword || !formData.newPassword || !formData.confirmPassword || isChanging)
                ? 'opacity-50 cursor-not-allowed'
                : ''
            }`}
          >
            {isChanging ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                {t('profile.changePasswordModal.changingButton')}
              </>
            ) : (
              <>
                <Lock size={16} />
                {t('profile.changePasswordModal.changeButton')}
              </>
            )}
          </button>
        </div>
      </form>
    </Modal>
  );
};

PasswordChangeModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onChangePassword: PropTypes.func.isRequired,
  darkMode: PropTypes.bool.isRequired
};

export default PasswordChangeModal;
