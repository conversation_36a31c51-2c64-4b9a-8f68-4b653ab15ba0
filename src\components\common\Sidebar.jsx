import { useRef, useEffect, useState } from 'react';
import { Sun, Moon, User, Mail } from 'lucide-react';
import gsap from 'gsap';
import PropTypes from 'prop-types';
import Button from './Button';
import Modal from './Modal';
import qQrng1 from '../../assets/q-qrng-1.svg';
import qQrng2 from '../../assets/q-qrng-2.svg';
import qQrng3 from '../../assets/q-qrng-3.svg';
import { useLanguage } from '../../hooks';

/**
 * Componente Sidebar reutilizable
 * Proporciona navegación lateral consistente para ambos dashboards
 */
const Sidebar = ({
  title,
  currentUser,
  darkMode,
  onToggleDarkMode,
  onLogout,
  navigationItems,
  activeSection,
  onSectionChange
}) => {
  const sidebarRef = useRef(null);
  const sunIconRef = useRef(null);
  const moonIconRef = useRef(null);
  const [showLogoutModal, setShowLogoutModal] = useState(false);
  const [currentAvatar, setCurrentAvatar] = useState(qQrng1);

  // Lista de avatares disponibles (solo 3)
  const avatarOptions = {
    'qrng1': qQrng1,
    'qrng2': qQrng2,
    'qrng3': qQrng3
  };
  const { t } = useLanguage();

  // Cargar avatar guardado y escuchar cambios
  useEffect(() => {
    const loadAvatar = () => {
      const avatarKey = currentUser ? `selectedAvatar_${currentUser.id}` : 'selectedAvatar';
      const savedAvatarId = localStorage.getItem(avatarKey);
      if (savedAvatarId && avatarOptions[savedAvatarId]) {
        setCurrentAvatar(avatarOptions[savedAvatarId]);
      } else {
        // Si no hay avatar guardado o no existe, usar por defecto
        setCurrentAvatar(qQrng1);
        localStorage.setItem(avatarKey, 'qrng1');
      }
    };

    // Cargar al inicio
    loadAvatar();

    // Escuchar cambios en localStorage
    const handleStorageChange = (e) => {
      const avatarKey = currentUser ? `selectedAvatar_${currentUser.id}` : 'selectedAvatar';
      if (e.key === avatarKey) {
        loadAvatar();
      }
    };

    window.addEventListener('storage', handleStorageChange);

    // También escuchar un evento personalizado para cambios en la misma pestaña
    const handleAvatarChange = () => {
      loadAvatar();
    };

    window.addEventListener('avatarChanged', handleAvatarChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('avatarChanged', handleAvatarChange);
    };
  }, [currentUser]);

  // Animaciones GSAP al montar (solo escritorio)
  useEffect(() => {
    if (window.innerWidth >= 768) {
      gsap.fromTo(
        sidebarRef.current,
        { x: -100, opacity: 0 },
        { x: 0, opacity: 1, duration: 1, ease: "power2.out" }
      );
    }
  }, []);

  // Animaciones de iconos según el modo
  useEffect(() => {
    if (darkMode) {
      // Modo oscuro: Sol rotando
      if (sunIconRef.current) {
        gsap.to(sunIconRef.current, {
          rotation: 360,
          duration: 4,
          ease: "none",
          repeat: -1
        });
      }
    } else {
      // Modo claro: Luna respirando
      if (moonIconRef.current) {
        gsap.to(moonIconRef.current, {
          scale: 1.2,
          opacity: 0.7,
          duration: 1.5,
          ease: "power2.inOut",
          repeat: -1,
          yoyo: true
        });
      }
    }

    // Limpiar animaciones al cambiar modo
    return () => {
      if (sunIconRef.current) gsap.killTweensOf(sunIconRef.current);
      if (moonIconRef.current) gsap.killTweensOf(moonIconRef.current);
    };
  }, [darkMode]);

  // Funciones para manejar el modal de logout
  const handleLogoutClick = () => {
    setShowLogoutModal(true);
  };

  const handleConfirmLogout = () => {
    setShowLogoutModal(false);
    onLogout();
  };

  const handleCancelLogout = () => {
    setShowLogoutModal(false);
  };

  return (
    <aside
      ref={sidebarRef}
      className={`w-64 sm:w-72 lg:w-80 h-full shadow-xl z-10 p-4 sm:p-6 flex flex-col justify-between transition-colors duration-500
      ${darkMode ? "bg-gray-800 text-white" : "bg-white text-gray-800"}`}
    >
      {/* Información del Usuario */}
      <div>
        {/* Logo combinado para admin y usuario */}
        {title === "Quantum Admin" ? (
          <div className="mb-4 text-center">
            <div className="flex items-center justify-center gap-3">
              {/* Avatar de admin (igual que usuario) */}
              <img
                src={currentAvatar}
                alt="Admin Avatar"
                className={`w-12 h-12 object-contain transition-all duration-300 ${
                  darkMode ? 'filter invert brightness-0 contrast-100' : ''
                }`}
              />
              {/* Logo SECURE horizontal */}
              <img
                src="/assets/sequre-logo-negro.svg"
                alt="SECURE Logo"
                className={`w-28 h-8 object-contain ${darkMode ? 'filter invert' : ''}`}
              />
            </div>
          </div>
        ) : title === "Quantum Usuario" ? (
          <div className="mb-4 text-center">
            <div className="flex items-center justify-center gap-3">
              {/* Icono de usuario */}
              <img
                src={currentAvatar}
                alt="Usuario Icon"
                className={`w-12 h-12 object-contain transition-all duration-300 ${
                  darkMode ? 'filter invert brightness-0 contrast-100' : ''
                }`}
              />
              {/* Logo SECURE horizontal */}
              <img
                src="/assets/sequre-logo-negro.svg"
                alt="SECURE Logo"
                className={`w-28 h-8 object-contain ${darkMode ? 'filter invert' : ''}`}
              />
            </div>
          </div>
        ) : (
          <h2 className="text-2xl font-bold mb-4 text-center">{title}</h2>
        )}
        
        {/* Información del usuario */}
        {currentUser && (
          <div className={`mb-6 sm:mb-8 p-3 sm:p-4 rounded-xl border ${darkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-200'} shadow-lg`}>
            <div className="flex items-center gap-2 sm:gap-3 mb-2">
              <User size={14} className="text-blue-500 sm:w-4 sm:h-4" />
              <span className="font-light tracking-wide text-xs sm:text-sm truncate">
                {currentUser.role === 'ADMIN' ? 'Super Admin' : `${currentUser.firstName} ${currentUser.lastName}`}
              </span>
            </div>
            <div className="flex items-center gap-2 sm:gap-3">
              <Mail size={14} className="text-green-500 sm:w-4 sm:h-4" />
              <span className={`text-xs font-light tracking-wide truncate ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                {currentUser.email}
              </span>
            </div>
          </div>
        )}

        {/* Navegación */}
        <nav className="space-y-3">
          {navigationItems.map((item) => (
            <button
              key={item.key}
              onClick={() => onSectionChange(item.key)}
              className={`flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-300 w-full text-left font-light tracking-wide ${
                activeSection === item.key
                  ? (darkMode ? 'bg-blue-900 text-blue-300 shadow-lg' : 'bg-blue-100 text-blue-700 shadow-lg')
                  : (darkMode ? 'hover:bg-gray-700 hover:shadow-md' : 'hover:bg-gray-100 hover:shadow-md')
              }`}
            >
              <item.icon size={18} /> <span>{item.label}</span>
            </button>
          ))}
        </nav>
      </div>

      {/* Botones elegantes como en el proyecto anterior */}
      <div className="flex flex-col gap-3">
        <button
          onClick={onToggleDarkMode}
          className={`w-full flex items-center justify-center gap-3 py-3 px-4 rounded-xl font-light tracking-wide shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 group ${
            darkMode
              ? "bg-gradient-to-r from-yellow-400 to-yellow-600 hover:from-yellow-500 hover:to-yellow-700 text-gray-900"
              : "bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white"
          }`}
        >
          <div className={`p-1 rounded-lg group-hover:bg-white/30 transition-all duration-300 ${
            darkMode ? "bg-gray-900/20" : "bg-white/20"
          }`}>
            {darkMode ? (
              <Sun ref={sunIconRef} size={16} className="text-gray-900" />
            ) : (
              <Moon ref={moonIconRef} size={16} className="text-white" />
            )}
          </div>
          <span className="font-light tracking-wide">
            {darkMode ? t('navigation.lightMode') : t('navigation.darkMode')}
          </span>
        </button>

        <button
          onClick={handleLogoutClick}
          className="w-full flex items-center justify-center gap-3 py-3 px-4 rounded-xl font-light tracking-wide shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 group bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white"
        >
          <div className="p-1 bg-white/20 rounded-lg group-hover:bg-white/30 transition-all duration-300">
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M3 3a1 1 0 00-1 1v12a1 1 0 102 0V4a1 1 0 00-1-1zm10.293 9.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L14.586 9H7a1 1 0 100 2h7.586l-1.293 1.293z" clipRule="evenodd" />
            </svg>
          </div>
          <span className="font-light tracking-wide">
            {t('navigation.logout')}
          </span>
        </button>
      </div>

      {/* Modal de confirmación de logout */}
      <Modal
        isOpen={showLogoutModal}
        onClose={handleCancelLogout}
        title={
          <div className="flex items-center gap-3">
            <svg
              className="w-6 h-6 text-red-500"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
              />
            </svg>
            <div>
              <h3 className="text-2xl font-light tracking-wide leading-relaxed text-gray-900 dark:text-white">
                {t('messages.confirmLogout')}
              </h3>
              <p className="text-sm font-light tracking-wide text-gray-600 dark:text-gray-400">
                {t('messages.confirmLogoutSubtitle')}
              </p>
            </div>
          </div>
        }
        maxWidth="max-w-lg"
        darkMode={darkMode}
      >
        {/* Contenido del modal */}
        <div className="space-y-4">
          {/* Mensaje de confirmación */}
          <div className={`p-4 rounded-xl border shadow-sm ${
            darkMode
              ? 'bg-red-900/10 border-red-700'
              : 'bg-red-50 border-red-200'
          }`}>
            <div className="flex items-start gap-3">
              <svg
                className="w-5 h-5 text-red-500 flex-shrink-0"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
                />
              </svg>
              <div className="flex-1">
                <h4 className="text-base font-light tracking-wide text-red-700 dark:text-red-300 mb-2">
                  {t('messages.confirmLogoutQuestion')}
                </h4>
                <p className="text-sm font-light tracking-wide text-red-600 dark:text-red-400">
                  {t('messages.confirmLogoutWarning')}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Footer con botones */}
        <div className="flex justify-center gap-3 mt-6 pt-4 border-t border-gray-200 dark:border-gray-600">
          <button
            onClick={handleCancelLogout}
            className="px-8 py-3 rounded-xl bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-light tracking-wide shadow-sm hover:shadow-md transform hover:scale-105 transition-all duration-300"
          >
            {t('common.cancel')}
          </button>
          <button
            onClick={handleConfirmLogout}
            className="px-8 py-3 rounded-xl bg-red-600 hover:bg-red-700 text-white font-light tracking-wide shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 flex items-center gap-2"
          >
            <svg
              className="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
              />
            </svg>
            {t('navigation.logout')}
          </button>
        </div>
      </Modal>
    </aside>
  );
};

Sidebar.propTypes = {
  title: PropTypes.string.isRequired,
  currentUser: PropTypes.object,
  darkMode: PropTypes.bool.isRequired,
  onToggleDarkMode: PropTypes.func.isRequired,
  onLogout: PropTypes.func.isRequired,
  navigationItems: PropTypes.arrayOf(PropTypes.shape({
    key: PropTypes.string.isRequired,
    label: PropTypes.string.isRequired,
    icon: PropTypes.elementType.isRequired
  })).isRequired,
  activeSection: PropTypes.string.isRequired,
  onSectionChange: PropTypes.func.isRequired
};

export default Sidebar;
