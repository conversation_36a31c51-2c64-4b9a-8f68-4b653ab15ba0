import { useState, useEffect } from "react";
import {
  Key,
  User,
  BarChart3,
  Calendar,
  Shield,
} from "lucide-react";
import { useAuth, useKeys, useUsers, useLanguage } from '../hooks/index';
import { DashboardLayout, DashboardStats } from '../components/dashboard';
import { KeyManagement, ProfileManagement } from '../components/dashboard/user';

const UsuarioDashboard = () => {
  const [activeSection, setActiveSection] = useState('dashboard');

  // Hooks para autenticación, llaves y usuarios
  const { user: currentUser, logout } = useAuth();
  const { t, currentLanguage } = useLanguage();
  const {
    keys: userKeys,
    statistics,
    isLoading: keysLoading,
    error: keysError,
    uploadToCTM,
    getKeysByUser,
    deleteKey,
    clearError
  } = useKeys();
  const {
    updateUser,
    isLoading: userLoading,
    error: userError,
    clearError: clearUserError
  } = useUsers();

  // <PERSON>gar llaves del usuario al montar el componente
  useEffect(() => {
    const loadUserKeys = async () => {
      if (currentUser && currentUser.id) {
        try {
          await getKeysByUser(currentUser.id);
        } catch {
          // Error loading user keys - handled by hooks
        }
      }
    };

    loadUserKeys();
  }, [currentUser, getKeysByUser]);

  // Manejar logout
  const handleLogout = () => {
    logout();
    
    // Limpiar storage
    sessionStorage.removeItem('token');
    sessionStorage.removeItem('userRole');
    sessionStorage.removeItem('tokenExpiry');
    sessionStorage.removeItem('loginTime');
    localStorage.removeItem('token');
    localStorage.removeItem('userRole');
    sessionStorage.setItem('wasLoggedOut', 'true');
    
    window.location.href = '/';
  };

  // Funciones para manejar llaves
  const handleUploadKey = async (keyData) => {
    const result = await uploadToCTM(keyData);
    // Recargar la lista de llaves después de subir exitosamente
    if (currentUser && currentUser.id) {
      await getKeysByUser(currentUser.id);
    }
    return result;
  };

  const handleDeleteKey = async (keyId) => {
    await deleteKey(keyId);
  };

  // Función para actualizar perfil
  const handleUpdateProfile = async (profileData) => {
    await updateUser(currentUser.id, profileData);
    // Los cambios se reflejan automáticamente sin necesidad de recargar
  };

  // Función para cambiar contraseña
  const handleChangePassword = async (passwordData) => {
    // Aquí deberías implementar la llamada al servicio de cambio de contraseña
    // Por ahora, simularemos la funcionalidad
    try {
      // Ejemplo de implementación:
      // await authService.changePassword(passwordData.currentPassword, passwordData.newPassword);
      console.log('Cambio de contraseña:', passwordData);

      // Mostrar mensaje de éxito (puedes implementar un toast o notificación)
      alert('Contraseña cambiada exitosamente');
    } catch (error) {
      console.error('Error al cambiar contraseña:', error);
      throw error;
    }
  };

  // Configuración de navegación
  const navigationItems = [
    { key: 'dashboard', label: t('navigation.dashboard'), icon: BarChart3 },
    { key: 'keys', label: t('navigation.keys'), icon: Key },
    { key: 'profile', label: t('navigation.profile'), icon: User }
  ];

  // Configuración de estadísticas principales
  const mainStats = [
    {
      icon: Key,
      title: t('dashboard.stats.myKeys'),
      value: statistics?.total || userKeys.length,
      description: t('dashboard.stats.keysGenerated'),
      iconColor: "text-blue-500",
      valueColor: "text-blue-600 dark:text-blue-400",
      percentage: 75
    },
    {
      icon: Shield,
      title: t('dashboard.stats.activeKeys'),
      value: statistics?.uploadedToCtm || userKeys.filter(key =>
        key.status === 'UPLOADED_TO_CTM' || key.uploadedToCtm === true
      ).length,
      description: t('dashboard.stats.keysActive'),
      iconColor: "text-green-500",
      valueColor: "text-green-600 dark:text-green-400",
      percentage: 75
    },
    {
      icon: Calendar,
      title: t('dashboard.stats.lastAccess'),
      value: new Date().toLocaleDateString(
        currentLanguage === 'es' ? 'es-ES' : 'en-US', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      }),
      description: t('dashboard.stats.lastLoginDate'),
      iconColor: "text-purple-500",
      valueColor: "text-purple-600 dark:text-purple-400",
      percentage: null // Sin porcentaje circular
    }
  ];

  // Configuración de estadísticas adicionales
  const miniStats = statistics ? [
    {
      value: statistics.successful,
      label: t('dashboard.miniStats.successful'),
      valueColor: "text-green-600 dark:text-green-400"
    },
    {
      value: statistics.failed,
      label: t('dashboard.miniStats.failed'),
      valueColor: "text-red-600 dark:text-red-400"
    },
    {
      value: statistics.uploadedToCtm,
      label: t('dashboard.miniStats.inCTM'),
      valueColor: "text-blue-600 dark:text-blue-400"
    },
    {
      value: (() => {
        if (!statistics.total || statistics.total === 0) return 0;
        const percentage = Math.round((statistics.successful / statistics.total) * 100);
        return Math.min(percentage, 100); // Asegurar que no exceda 100%
      })(),
      label: t('dashboard.miniStats.successRate'),
      valueColor: "text-purple-600 dark:text-purple-400"
    }
  ] : [];

  const renderContent = (darkMode = false) => {
    switch (activeSection) {
      case 'dashboard':
        return (
          <>
            <div className="text-center sm:text-left mb-6 sm:mb-8">
              <h1 className="text-2xl sm:text-3xl font-light tracking-wide leading-relaxed mb-2 sm:mb-4 text-gray-900 dark:text-white">
                {t('dashboard.welcome', { name: currentUser ? `${currentUser.firstName} ${currentUser.lastName}` : 'Usuario' })}
              </h1>
              <p className="text-sm sm:text-base text-gray-600 dark:text-gray-300 font-light tracking-wide">
                {t('dashboard.subtitle')}
              </p>
            </div>

            <DashboardStats
              mainStats={mainStats}
              miniStats={miniStats}
              isLoading={keysLoading}
              error={keysError}
              onClearError={clearError}
            />
          </>
        );

      case 'keys':
        return (
          <KeyManagement
            keys={userKeys}
            isLoading={keysLoading}
            error={keysError}
            onClearError={clearError}
            onUploadKey={handleUploadKey}
            onDeleteKey={handleDeleteKey}
            darkMode={darkMode}
          />
        );

      case 'profile':
        return (
          <ProfileManagement
            currentUser={currentUser}
            isLoading={userLoading}
            error={userError}
            onClearError={clearUserError}
            onUpdateProfile={handleUpdateProfile}
            onChangePassword={handleChangePassword}
            darkMode={darkMode}
          />
        );
        
      default:
        return null;
    }
  };

  // Componente wrapper que recibe darkMode del DashboardLayout
  const ContentWrapper = ({ darkMode }) => {
    return renderContent(darkMode);
  };

  return (
    <DashboardLayout
      title="Quantum Usuario"
      currentUser={currentUser}
      navigationItems={navigationItems}
      activeSection={activeSection}
      onSectionChange={setActiveSection}
      onLogout={handleLogout}
      expectedRole={['usuario', 'USER', 'user']}
    >
      <ContentWrapper />
    </DashboardLayout>
  );
};

export default UsuarioDashboard;
