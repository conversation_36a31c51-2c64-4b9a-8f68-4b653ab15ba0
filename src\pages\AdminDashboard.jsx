import { useState, useEffect } from "react";
import {
  Users,
  KeyRound,
  BarChart3,
  Activity,
  User,
} from "lucide-react";
import { useAuth, useUsers, useLanguage } from '../hooks/index';
import { useAdminStats } from '../hooks/useAdminStats';
import { DashboardLayout, DashboardStats } from '../components/dashboard';
import { UserManagement, AdminKeysSection, AdminProfileManagement } from '../components/dashboard/admin';

const AdminDashboard = () => {
  const [activeSection, setActiveSection] = useState('dashboard');

  // Hooks para autenticación, usuarios e idioma
  const { user: currentUser, logout } = useAuth();
  const { t } = useLanguage();
  const {
    users,
    isLoading: usersLoading,
    error: usersError,
    getAllUsers,
    createUser,
    updateUser,
    deleteUser,
    clearError
  } = useUsers();

  // Hook para estadísticas del admin
  const {
    stats,
    usersWithKeys,
    isLoading: statsLoading,
    error: statsError,
    loadStats,
    clearError: clearStatsError
  } = useAdminStats();

  // Cargar usuarios y estadísticas al montar el componente
  useEffect(() => {
    const loadData = async () => {
      try {
        // Cargar usuarios y estadísticas en paralelo
        await Promise.all([
          getAllUsers(),
          loadStats()
        ]);
      } catch {
        // Error loading admin data - handled by hooks
      }
    };

    if (currentUser && (currentUser.role === 'ADMIN' || currentUser.role === 'admin')) {
      loadData();
    }
  }, [currentUser, getAllUsers, loadStats]);

  // Manejar logout
  const handleLogout = () => {
    logout();
  };

  // Funciones para gestión de usuarios
  const handleUpdateUser = async (userId, userData) => {
    await updateUser(userId, userData);
    // Refrescar datos después de actualizar
    await Promise.all([getAllUsers(), loadStats()]);
  };

  const handleCreateUser = async (userData) => {
    await createUser(userData);
    // Refrescar datos después de crear
    await Promise.all([getAllUsers(), loadStats()]);
  };

  const handleDeleteUser = async (userId) => {
    try {
      // Usar el hook deleteUser que ya maneja la actualización de la lista
      await deleteUser(userId);

      // Refrescar estadísticas después de eliminar
      await loadStats();

      // TODO: Mostrar notificación elegante de éxito (reemplazar alert)
      alert('Usuario eliminado exitosamente');

    } catch (error) {
      console.error('Error eliminando usuario:', error);
      // TODO: Mostrar notificación elegante de error (reemplazar alert)
      alert('Error al eliminar el usuario: ' + error.message);
    }
  };

  // Función para actualizar perfil del admin
  const handleUpdateProfile = async (profileData) => {
    await updateUser(currentUser.id, profileData);
    // Los cambios se reflejan automáticamente sin necesidad de recargar
  };

  // Función para cambiar contraseña del admin
  const handleChangePassword = async (passwordData) => {
    // Aquí deberías implementar la llamada al servicio de cambio de contraseña
    // Por ahora, simularemos la funcionalidad
    try {
      // Ejemplo de implementación:
      // await authService.changePassword(passwordData.currentPassword, passwordData.newPassword);
      console.log('Cambio de contraseña:', passwordData);

      // Simular éxito
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Mostrar mensaje de éxito (podrías usar un toast o notificación)
      alert('Contraseña cambiada exitosamente');
    } catch (error) {
      console.error('Error al cambiar contraseña:', error);
      throw new Error('Error al cambiar la contraseña. Inténtalo de nuevo.');
    }
  };

  // Configuración de navegación
  const navigationItems = [
    { key: 'dashboard', label: t('navigation.dashboard'), icon: BarChart3 },
    { key: 'users', label: t('admin.navigation.users'), icon: Users },
    { key: 'keys', label: t('admin.navigation.keys'), icon: KeyRound },
    { key: 'profile', label: t('navigation.profile'), icon: User }
  ];

  // Configuración de estadísticas principales con porcentajes y delays
  const mainStats = [
    {
      icon: Users,
      title: t('admin.stats.totalUsers'),
      value: stats.totalUsers,
      description: t('admin.stats.totalUsersDesc'),
      iconColor: "text-blue-500",
      valueColor: "text-blue-600 dark:text-blue-400",
      percentage: Math.min((stats.totalUsers / 100) * 100, 100), // Porcentaje basado en objetivo
      delay: 0
    },
    {
      icon: KeyRound,
      title: t('admin.stats.activeKeys'),
      value: stats.activeKeys,
      description: t('admin.stats.activeKeysDesc'),
      iconColor: "text-green-500",
      valueColor: "text-green-600 dark:text-green-400",
      percentage: Math.min((stats.activeKeys / 50) * 100, 100), // Porcentaje basado en objetivo
      delay: 1
    },
    {
      icon: Activity,
      title: t('admin.stats.activeUsers'),
      value: stats.activeUsers,
      description: t('admin.stats.activeUsersDesc'),
      iconColor: "text-purple-500",
      valueColor: "text-purple-600 dark:text-purple-400",
      percentage: Math.min((stats.activeUsers / stats.totalUsers) * 100, 100), // Porcentaje de usuarios activos
      delay: 2
    }
  ];

  // Configuración de estadísticas adicionales
  const miniStats = [
    {
      value: stats.totalKeys,
      label: t('admin.stats.totalKeys'),
      valueColor: "text-gray-900 dark:text-white"
    },
    {
      value: stats.successfulKeys,
      label: t('admin.stats.successfulKeys'),
      valueColor: "text-green-600 dark:text-green-400"
    },
    {
      value: stats.failedKeys,
      label: t('admin.stats.failedKeys'),
      valueColor: "text-red-600 dark:text-red-400"
    },
    {
      value: stats.uploadedToCtm,
      label: t('admin.stats.inCTM'),
      valueColor: "text-blue-600 dark:text-blue-400"
    }
  ];

  const renderContent = (darkMode = false) => {
    switch (activeSection) {
      case 'dashboard':
        return (
          <>
            <h1 className="text-3xl font-light tracking-wide leading-relaxed mb-4">
              {t('admin.welcome')}
            </h1>
            <p className="text-gray-600 dark:text-white mb-6 font-light tracking-wide">
              {t('admin.subtitle')}
            </p>

            <DashboardStats
              mainStats={mainStats}
              miniStats={miniStats}
              isLoading={statsLoading}
              error={statsError}
              onClearError={clearStatsError}
            />
          </>
        );

      case 'users':
        return (
          <UserManagement
            users={users}
            isLoading={usersLoading}
            error={usersError}
            onClearError={clearError}
            onUpdateUser={handleUpdateUser}
            onCreateUser={handleCreateUser}
            onDeleteUser={handleDeleteUser}
            darkMode={darkMode}
          />
        );

      case 'keys':
        return (
          <AdminKeysSection
            usersWithKeys={usersWithKeys}
            isLoading={statsLoading}
            darkMode={darkMode}
          />
        );

      case 'profile':
        return (
          <AdminProfileManagement
            currentUser={currentUser}
            isLoading={usersLoading}
            error={usersError}
            onClearError={clearError}
            onUpdateProfile={handleUpdateProfile}
            onChangePassword={handleChangePassword}
            darkMode={darkMode}
          />
        );

      default:
        return null;
    }
  };

  // Componente wrapper que recibe darkMode del DashboardLayout
  const ContentWrapper = ({ darkMode }) => {
    return renderContent(darkMode);
  };

  return (
    <DashboardLayout
      title="Quantum Admin"
      currentUser={currentUser}
      navigationItems={navigationItems}
      activeSection={activeSection}
      onSectionChange={setActiveSection}
      onLogout={handleLogout}
      expectedRole={['admin', 'ADMIN']}
    >
      <ContentWrapper />
    </DashboardLayout>
  );
};

export default AdminDashboard;
