import { useState, useEffect } from 'react';
import { Save, User, Mail, Building, Camera } from 'lucide-react';
import qQrng1 from '../../../assets/q-qrng-1.svg';
import qQrng2 from '../../../assets/q-qrng-2.svg';
import qQrng3 from '../../../assets/q-qrng-3.svg';
import PropTypes from 'prop-types';
import { Modal, Button, FormField, ErrorAlert } from '../../common';
import { useLanguage } from '../../../hooks';

/**
 * Modal para editar perfil del usuario
 */
const ProfileEditModal = ({ isOpen, currentUser, onClose, onUpdate, darkMode }) => {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    company: ''
  });
  const [selectedAvatar, setSelectedAvatar] = useState(qQrng1);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState(null);

  // Lista de avatares disponibles (solo 3)
  const avatarOptions = [
    { id: 'qrng1', src: qQrng1, name: 'Q-QRNG 1' },
    { id: 'qrng2', src: qQrng2, name: 'Q-QRNG 2' },
    { id: 'qrng3', src: qQrng3, name: 'Q-QRNG 3' }
  ];
  const { t } = useLanguage();

  useEffect(() => {
    if (currentUser) {
      setFormData({
        firstName: currentUser.firstName || '',
        lastName: currentUser.lastName || '',
        email: currentUser.email || '',
        company: currentUser.company || ''
      });
    }

    // Cargar avatar guardado o usar por defecto (qrng1)
    const avatarKey = currentUser ? `selectedAvatar_${currentUser.id}` : 'selectedAvatar';
    const savedAvatarId = localStorage.getItem(avatarKey);
    if (savedAvatarId) {
      const savedAvatar = avatarOptions.find(avatar => avatar.id === savedAvatarId);
      if (savedAvatar) {
        setSelectedAvatar(savedAvatar.src);
      } else {
        // Si el avatar guardado ya no existe, usar por defecto
        setSelectedAvatar(qQrng1);
        localStorage.setItem(avatarKey, 'qrng1');
      }
    } else {
      // Si no hay avatar guardado, usar por defecto
      setSelectedAvatar(qQrng1);
      localStorage.setItem(avatarKey, 'qrng1');
    }
  }, [currentUser]);

  const handleChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleAvatarSelect = (avatar) => {
    setSelectedAvatar(avatar.src);
    // Guardar en localStorage con clave específica del usuario
    const avatarKey = currentUser ? `selectedAvatar_${currentUser.id}` : 'selectedAvatar';
    localStorage.setItem(avatarKey, avatar.id);
    // Disparar evento para actualizar el Sidebar
    window.dispatchEvent(new Event('avatarChanged'));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSaving(true);
    setError(null);
    
    try {
      // Solo enviar los campos que el usuario puede modificar
      const allowedFields = {
        firstName: formData.firstName,
        lastName: formData.lastName,
        email: formData.email,
        company: formData.company
      };
      
      await onUpdate(allowedFields);

      // Asegurar que el avatar se mantenga guardado
      const currentAvatarId = avatarOptions.find(avatar => avatar.src === selectedAvatar)?.id;
      if (currentAvatarId) {
        const avatarKey = currentUser ? `selectedAvatar_${currentUser.id}` : 'selectedAvatar';
        localStorage.setItem(avatarKey, currentAvatarId);
        // Disparar evento para asegurar sincronización
        window.dispatchEvent(new Event('avatarChanged'));
      }
    } catch (err) {
      setError(err.message || t('profile.editProfileModal.updateError'));
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={
        <div className="flex items-center gap-3">
          <User size={24} className="text-green-500" />
          <div>
            <h3 className="text-2xl font-light tracking-wide leading-relaxed text-gray-900 dark:text-white">
              {t('profile.editProfileModal.title')}
            </h3>
            <p className="text-sm font-light tracking-wide text-gray-600 dark:text-gray-400">
              {t('profile.editProfileModal.subtitle')}
            </p>
          </div>
        </div>
      }
      maxWidth="max-w-lg"
      darkMode={darkMode}
    >

      <div className="space-y-4">
        <ErrorAlert error={error} onClose={() => setError(null)} />

        {/* Información Personal */}
        <div className={`p-4 rounded-xl border shadow-sm ${
          darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
        }`}>
          <h4 className="text-lg font-light tracking-wide mb-4 flex items-center gap-3 text-gray-900 dark:text-white">
            <User size={22} className="text-blue-500" />
            {t('profile.editProfileModal.personalInfo')}
          </h4>
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-light tracking-wide text-gray-600 dark:text-gray-400 mb-3">
                {t('profile.editProfileModal.firstName')}
              </label>
              <input
                type="text"
                value={formData.firstName}
                onChange={(e) => handleChange('firstName', e.target.value)}
                required
                disabled={isSaving}
                className={`w-full px-4 py-4 rounded-xl border-2 font-light tracking-wide transition-all duration-300 focus:ring-4 focus:ring-blue-100 focus:border-blue-500 ${
                  darkMode
                    ? 'bg-gray-900/50 border-gray-600 text-white placeholder-gray-400'
                    : 'bg-gray-50 border-gray-200 text-gray-900 placeholder-gray-500'
                } ${isSaving ? 'opacity-50 cursor-not-allowed' : ''}`}
                placeholder={t('profile.editProfileModal.firstNamePlaceholder')}
              />
            </div>

            <div>
              <label className="block text-sm font-light tracking-wide text-gray-600 dark:text-gray-400 mb-3">
                {t('profile.editProfileModal.lastName')}
              </label>
              <input
                type="text"
                value={formData.lastName}
                onChange={(e) => handleChange('lastName', e.target.value)}
                required
                disabled={isSaving}
                className={`w-full px-4 py-4 rounded-xl border-2 font-light tracking-wide transition-all duration-300 focus:ring-4 focus:ring-blue-100 focus:border-blue-500 ${
                  darkMode
                    ? 'bg-gray-900/50 border-gray-600 text-white placeholder-gray-400'
                    : 'bg-gray-50 border-gray-200 text-gray-900 placeholder-gray-500'
                } ${isSaving ? 'opacity-50 cursor-not-allowed' : ''}`}
                placeholder={t('profile.editProfileModal.lastNamePlaceholder')}
              />
            </div>
          </div>
        </div>

        {/* Información de Contacto */}
        <div className={`p-4 rounded-xl border shadow-sm ${
          darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
        }`}>
          <h4 className="text-lg font-light tracking-wide mb-4 flex items-center gap-3 text-gray-900 dark:text-white">
            <Mail size={22} className="text-green-500" />
            {t('profile.editProfileModal.contactInfo')}
          </h4>
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-light tracking-wide text-gray-600 dark:text-gray-400 mb-3">
                {t('profile.editProfileModal.email')}
              </label>
              <input
                type="email"
                value={formData.email}
                onChange={(e) => handleChange('email', e.target.value)}
                required
                disabled={isSaving}
                className={`w-full px-4 py-4 rounded-xl border-2 font-light tracking-wide transition-all duration-300 focus:ring-4 focus:ring-green-100 focus:border-green-500 ${
                  darkMode
                    ? 'bg-gray-900/50 border-gray-600 text-white placeholder-gray-400'
                    : 'bg-gray-50 border-gray-200 text-gray-900 placeholder-gray-500'
                } ${isSaving ? 'opacity-50 cursor-not-allowed' : ''}`}
                placeholder={t('profile.editProfileModal.emailPlaceholder')}
              />
            </div>

            <div>
              <label className="block text-sm font-light tracking-wide text-gray-600 dark:text-gray-400 mb-3">
                {t('profile.editProfileModal.company')}
              </label>
              <input
                type="text"
                value={formData.company}
                onChange={(e) => handleChange('company', e.target.value)}
                disabled={isSaving}
                className={`w-full px-4 py-4 rounded-xl border-2 font-light tracking-wide transition-all duration-300 focus:ring-4 focus:ring-green-100 focus:border-green-500 ${
                  darkMode
                    ? 'bg-gray-900/50 border-gray-600 text-white placeholder-gray-400'
                    : 'bg-gray-50 border-gray-200 text-gray-900 placeholder-gray-500'
                } ${isSaving ? 'opacity-50 cursor-not-allowed' : ''}`}
                placeholder={t('profile.editProfileModal.companyPlaceholder')}
              />
            </div>
          </div>
        </div>

        {/* Selección de Avatar */}
        <div className={`p-4 rounded-xl border shadow-sm ${
          darkMode ? 'bg-gray-700 border-gray-600' : 'bg-white border-gray-200'
        }`}>
          <h4 className="text-lg font-light tracking-wide mb-4 flex items-center gap-3 text-gray-900 dark:text-white">
            <Camera size={22} className="text-purple-500" />
            {t('profile.editProfileModal.selectAvatar')}
          </h4>

          <div className="space-y-4">
            {/* Avatar actual */}
            <div className="flex items-center gap-4">
              <div className="relative">
                <img
                  src={selectedAvatar}
                  alt={t('profile.editProfileModal.currentAvatar')}
                  className={`w-16 h-16 object-contain rounded-full border-2 border-purple-500 shadow-lg transition-all duration-300 ${
                    darkMode ? 'filter invert brightness-0 contrast-100' : ''
                  }`}
                />
              </div>
              <div>
                <p className="text-sm font-light tracking-wide text-gray-900 dark:text-white">
                  {t('profile.editProfileModal.currentAvatar')}
                </p>
                <p className="text-xs font-light tracking-wide text-gray-600 dark:text-gray-400">
                  {t('profile.editProfileModal.selectAvatarDescription')}
                </p>
              </div>
            </div>

            {/* Opciones de avatares */}
            <div className="grid grid-cols-3 gap-3">
              {avatarOptions.map((avatar) => (
                <button
                  key={avatar.id}
                  type="button"
                  onClick={() => handleAvatarSelect(avatar)}
                  className={`group p-3 rounded-xl transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 ${
                    selectedAvatar === avatar.src
                      ? 'bg-gradient-to-br from-purple-50 to-blue-50 dark:from-purple-900/30 dark:to-blue-900/30 border-2 border-purple-500 shadow-lg'
                      : 'hover:bg-gray-50 dark:hover:bg-gray-600/50 border-2 border-transparent hover:border-gray-300 dark:hover:border-gray-500'
                  }`}
                  title={avatar.name}
                >
                  <div className="flex flex-col items-center gap-2">
                    <img
                      src={avatar.src}
                      alt={avatar.name}
                      className={`w-12 h-12 object-contain rounded-full group-hover:scale-110 transition-all duration-200 ${
                        darkMode ? 'filter invert brightness-0 contrast-100' : ''
                      }`}
                    />
                    <span className="text-xs font-light tracking-wide text-gray-600 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white text-center">
                      {avatar.name}
                    </span>
                  </div>
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Información de Configuración */}
        <div className={`p-4 rounded-xl border shadow-sm ${
          darkMode ? 'bg-blue-900/10 border-blue-700' : 'bg-blue-50 border-blue-200'
        }`}>
          <h4 className="text-lg font-light tracking-wide mb-4 flex items-center gap-3 text-gray-900 dark:text-white">
            <Building size={22} className="text-blue-500" />
            {t('profile.editProfileModal.serviceConfig')}
          </h4>
          <p className="text-sm font-light tracking-wide text-gray-600 dark:text-gray-400 leading-relaxed">
            {t('profile.editProfileModal.serviceConfigNote')}
          </p>
        </div>

        {/* Botones */}
        <form onSubmit={handleSubmit}>
          <div className="flex justify-center gap-3 mt-6 pt-4 border-t border-gray-200 dark:border-gray-600">
            <button
              type="button"
              onClick={onClose}
              disabled={isSaving}
              className={`px-8 py-3 rounded-xl font-light tracking-wide transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl ${
                darkMode
                  ? 'bg-gray-700 hover:bg-gray-600 text-white border border-gray-600'
                  : 'bg-gray-100 hover:bg-gray-200 text-gray-700 border border-gray-300'
              } ${isSaving ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              {t('common.cancel')}
            </button>
            <button
              type="submit"
              disabled={isSaving}
              className={`px-8 py-3 rounded-xl font-light tracking-wide transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white flex items-center justify-center gap-2 ${
                isSaving ? 'opacity-50 cursor-not-allowed' : ''
              }`}
            >
              {isSaving ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  {t('profile.editProfileModal.savingButton')}
                </>
              ) : (
                <>
                  <Save size={16} />
                  {t('profile.editProfileModal.saveButton')}
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </Modal>
  );
};

ProfileEditModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  currentUser: PropTypes.object,
  onClose: PropTypes.func.isRequired,
  onUpdate: PropTypes.func.isRequired,
  darkMode: PropTypes.bool.isRequired
};

export default ProfileEditModal;
