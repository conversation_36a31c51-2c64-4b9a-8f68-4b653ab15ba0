import { useState, useEffect, useRef } from 'react';
import {
  Search,
  Filter,
  Key,
  User,
  Calendar,
  CheckCircle,
  XCircle,
  Clock,
  Eye,
  BarChart3,
  Users,
  Activity,
  ChevronDown,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import PropTypes from 'prop-types';
import { useLanguage } from '../../../hooks';
import { UserKeysModal, KeyDetailModal } from './index';

/**
 * Sección completa de gestión de llaves para AdminDashboard
 * Centraliza toda la funcionalidad relacionada con llaves
 */
const AdminKeysSection = ({ usersWithKeys, isLoading, darkMode }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedUser, setSelectedUser] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [selectedAlgorithm, setSelectedAlgorithm] = useState('all');
  const [showUserKeysModal, setShowUserKeysModal] = useState(false);
  const [showKeyDetailModal, setShowKeyDetailModal] = useState(false);
  const [selectedUserForKeys, setSelectedUserForKeys] = useState(null);
  const [selectedKey, setSelectedKey] = useState(null);

  // Estados para paginación
  const [currentPage, setCurrentPage] = useState(1);
  const [isAnimating, setIsAnimating] = useState(false);
  const [animationDirection, setAnimationDirection] = useState('');
  const keysPerPage = 5;

  // Estados para dropdowns custom
  const [isUserDropdownOpen, setIsUserDropdownOpen] = useState(false);
  const [isStatusDropdownOpen, setIsStatusDropdownOpen] = useState(false);
  const [isAlgorithmDropdownOpen, setIsAlgorithmDropdownOpen] = useState(false);

  // Referencias para dropdowns
  const userDropdownRef = useRef(null);
  const statusDropdownRef = useRef(null);
  const algorithmDropdownRef = useRef(null);
  const [userKeys, setUserKeys] = useState([]);
  const { t } = useLanguage();

  // Cerrar dropdowns al hacer click fuera
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (userDropdownRef.current && !userDropdownRef.current.contains(event.target)) {
        setIsUserDropdownOpen(false);
      }
      if (statusDropdownRef.current && !statusDropdownRef.current.contains(event.target)) {
        setIsStatusDropdownOpen(false);
      }
      if (algorithmDropdownRef.current && !algorithmDropdownRef.current.contains(event.target)) {
        setIsAlgorithmDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Obtener todas las llaves de todos los usuarios
  const allKeys = usersWithKeys.flatMap(user => 
    (user.keys || []).map(key => ({
      ...key,
      userName: `${user.firstName} ${user.lastName}`,
      userEmail: user.email,
      userId: user.id
    }))
  );

  // Filtrar llaves según los criterios
  const filteredKeys = allKeys.filter(key => {
    const matchesSearch = 
      key.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      key.userName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      key.algorithm?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesUser = selectedUser === 'all' || key.userId === selectedUser;
    const matchesStatus = selectedStatus === 'all' || key.status === selectedStatus;
    const matchesAlgorithm = selectedAlgorithm === 'all' || key.algorithm === selectedAlgorithm;

    return matchesSearch && matchesUser && matchesStatus && matchesAlgorithm;
  });

  // Estadísticas globales
  const stats = {
    totalKeys: allKeys.length,
    activeKeys: allKeys.filter(key => key.status === 'uploaded_to_ctm').length,
    failedKeys: allKeys.filter(key => key.status === 'failed').length,
    pendingKeys: allKeys.filter(key => key.status === 'pending').length,
    totalUsers: usersWithKeys.filter(user => user.keys && user.keys.length > 0).length
  };

  // Obtener opciones únicas para filtros
  const uniqueUsers = usersWithKeys.filter(user => user.keys && user.keys.length > 0);
  const uniqueStatuses = [...new Set(allKeys.map(key => key.status))];
  const uniqueAlgorithms = [...new Set(allKeys.map(key => key.algorithm))];

  // Lógica de paginación
  const totalPages = Math.ceil(filteredKeys.length / keysPerPage);
  const startIndex = (currentPage - 1) * keysPerPage;
  const endIndex = startIndex + keysPerPage;
  const currentKeys = filteredKeys.slice(startIndex, endIndex);

  // Resetear página cuando cambien los filtros
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, selectedUser, selectedStatus, selectedAlgorithm]);

  // Funciones de paginación con animación
  const handlePageChange = (newPage, direction) => {
    if (isAnimating || newPage === currentPage || newPage < 1 || newPage > totalPages) return;

    setIsAnimating(true);
    setAnimationDirection(direction);

    // Cambiar página inmediatamente y luego quitar animación
    setCurrentPage(newPage);

    setTimeout(() => {
      setIsAnimating(false);
      setAnimationDirection('');
    }, 300);
  };

  const goToNextPage = () => {
    if (currentPage < totalPages) {
      handlePageChange(currentPage + 1, 'next');
    }
  };

  const goToPrevPage = () => {
    if (currentPage > 1) {
      handlePageChange(currentPage - 1, 'prev');
    }
  };

  const handleViewUserKeys = (user) => {
    setSelectedUserForKeys(user);
    setUserKeys(user.keys || []);
    setShowUserKeysModal(true);
  };

  const handleViewKeyDetail = (key) => {
    setSelectedKey(key);
    setShowKeyDetailModal(true);
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'uploaded_to_ctm':
        return <CheckCircle size={16} className="text-green-500" />;
      case 'failed':
        return <XCircle size={16} className="text-red-500" />;
      case 'pending':
        return <Clock size={16} className="text-yellow-500" />;
      default:
        return <Clock size={16} className="text-gray-500" />;
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'uploaded_to_ctm':
        return t('keys.status.uploaded_to_ctm');
      case 'failed':
        return t('keys.status.failed');
      case 'pending':
        return t('keys.status.pending');
      default:
        return status;
    }
  };

  // Componente de paginación estilo Gmail
  const PaginationControls = () => {
    if (filteredKeys.length === 0) return null;

    const startItem = startIndex + 1;
    const endItem = Math.min(endIndex, filteredKeys.length);

    return (
      <div className="flex items-center justify-between text-sm font-light tracking-wide text-gray-600 dark:text-gray-400">
        <span>
          {startItem}-{endItem} de {filteredKeys.length}
        </span>

        <div className="flex items-center gap-1">
          <button
            onClick={goToPrevPage}
            disabled={currentPage === 1 || isAnimating}
            className={`p-1.5 rounded border transition-all duration-200 ${
              currentPage === 1 || isAnimating
                ? 'border-gray-200 bg-gray-100 text-gray-400 cursor-not-allowed dark:border-gray-700 dark:bg-gray-800 dark:text-gray-600'
                : 'border-gray-200 bg-white text-gray-600 hover:bg-gray-100 hover:border-gray-300 hover:scale-105 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
            }`}
            title="Página anterior"
          >
            <ChevronLeft size={14} />
          </button>

          <button
            onClick={goToNextPage}
            disabled={currentPage === totalPages || isAnimating}
            className={`p-1.5 rounded border transition-all duration-200 ${
              currentPage === totalPages || isAnimating
                ? 'border-gray-200 bg-gray-100 text-gray-400 cursor-not-allowed dark:border-gray-700 dark:bg-gray-800 dark:text-gray-600'
                : 'border-gray-200 bg-white text-gray-600 hover:bg-gray-100 hover:border-gray-300 hover:scale-105 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
            }`}
            title="Página siguiente"
          >
            <ChevronRight size={14} />
          </button>
        </div>
      </div>
    );
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
      </div>
    );
  }

  return (
    <>
      <div className="space-y-8">
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-light tracking-wide leading-relaxed text-gray-900 dark:text-white">
            {t('admin.keys.title')}
          </h1>
          <p className="text-lg font-light tracking-wide text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed">
            {t('admin.keys.subtitle')}
          </p>
        </div>

        {/* Estadísticas principales - Compactas */}
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-3">
          <div className="p-3 rounded-xl border shadow-sm bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
            <div className="flex items-center gap-2">
              <div className="p-1.5 rounded-lg bg-blue-50 dark:bg-blue-900/20">
                <Key size={14} className="text-blue-500" />
              </div>
              <div>
                <p className="text-lg font-light tracking-wide text-blue-600 dark:text-blue-400">
                  {stats.totalKeys}
                </p>
                <p className="text-xs font-light tracking-wide text-gray-600 dark:text-gray-400">
                  {t('admin.stats.totalKeys')}
                </p>
              </div>
            </div>
          </div>

          <div className="p-3 rounded-xl border shadow-sm bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
            <div className="flex items-center gap-2">
              <div className="p-1.5 rounded-lg bg-green-50 dark:bg-green-900/20">
                <CheckCircle size={14} className="text-green-500" />
              </div>
              <div>
                <p className="text-lg font-light tracking-wide text-green-600 dark:text-green-400">
                  {stats.activeKeys}
                </p>
                <p className="text-xs font-light tracking-wide text-gray-600 dark:text-gray-400">
                  {t('admin.stats.activeKeys')}
                </p>
              </div>
            </div>
          </div>

          <div className="p-3 rounded-xl border shadow-sm bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
            <div className="flex items-center gap-2">
              <div className="p-1.5 rounded-lg bg-red-50 dark:bg-red-900/20">
                <XCircle size={14} className="text-red-500" />
              </div>
              <div>
                <p className="text-lg font-light tracking-wide text-red-600 dark:text-red-400">
                  {stats.failedKeys}
                </p>
                <p className="text-xs font-light tracking-wide text-gray-600 dark:text-gray-400">
                  {t('admin.stats.failed')}
                </p>
              </div>
            </div>
          </div>

          <div className="p-3 rounded-xl border shadow-sm bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
            <div className="flex items-center gap-2">
              <div className="p-1.5 rounded-lg bg-yellow-50 dark:bg-yellow-900/20">
                <Clock size={14} className="text-yellow-500" />
              </div>
              <div>
                <p className="text-lg font-light tracking-wide text-yellow-600 dark:text-yellow-400">
                  {stats.pendingKeys}
                </p>
                <p className="text-xs font-light tracking-wide text-gray-600 dark:text-gray-400">
                  {t('admin.stats.pending')}
                </p>
              </div>
            </div>
          </div>

          <div className="p-3 rounded-xl border shadow-sm bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
            <div className="flex items-center gap-2">
              <div className="p-1.5 rounded-lg bg-purple-50 dark:bg-purple-900/20">
                <Users size={14} className="text-purple-500" />
              </div>
              <div>
                <p className="text-lg font-light tracking-wide text-purple-600 dark:text-purple-400">
                  {stats.totalUsers}
                </p>
                <p className="text-xs font-light tracking-wide text-gray-600 dark:text-gray-400">
                  {t('admin.stats.usersWithKeys')}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Filtros y búsqueda */}
        <div className="p-4 rounded-xl border shadow-sm bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
            {/* Búsqueda */}
            <div className="relative">
              <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder={t('admin.stats.searchPlaceholder')}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className={`w-full pl-10 pr-4 py-2 border rounded-lg font-light tracking-wide focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors duration-200 ${
                  darkMode
                    ? 'border-gray-600 bg-gray-700 text-white placeholder-gray-400'
                    : 'border-gray-300 bg-white text-gray-900 placeholder-gray-500'
                }`}
              />
            </div>

            {/* Filtro por usuario - Custom Dropdown */}
            <div className="relative" ref={userDropdownRef}>
              <button
                type="button"
                onClick={() => setIsUserDropdownOpen(!isUserDropdownOpen)}
                className={`w-full px-4 py-2 border rounded-lg font-light tracking-wide focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 flex items-center justify-between ${
                  darkMode
                    ? 'border-gray-600 bg-gray-700 text-white hover:bg-gray-600'
                    : 'border-gray-300 bg-white text-gray-900 hover:bg-gray-50'
                } cursor-pointer`}
              >
                <span>
                  {selectedUser === 'all'
                    ? t('admin.stats.allUsers')
                    : (() => {
                        const user = uniqueUsers.find(u => u.id === selectedUser);
                        return user ? `${user.firstName} ${user.lastName}` : t('admin.stats.allUsers');
                      })()
                  }
                </span>
                <ChevronDown
                  size={16}
                  className={`transition-transform duration-200 ${isUserDropdownOpen ? 'rotate-180' : ''} ${
                    darkMode ? 'text-gray-400' : 'text-gray-500'
                  }`}
                />
              </button>

              {isUserDropdownOpen && (
                <div className={`absolute top-full left-0 right-0 mt-1 border rounded-lg shadow-lg z-50 overflow-hidden ${
                  darkMode
                    ? 'border-gray-600 bg-gray-700'
                    : 'border-gray-300 bg-white'
                }`}>
                  <button
                    type="button"
                    onClick={() => {
                      setSelectedUser('all');
                      setIsUserDropdownOpen(false);
                    }}
                    className={`w-full text-left px-4 py-3 font-light tracking-wide transition-all duration-200 ${
                      selectedUser === 'all'
                        ? darkMode
                          ? 'bg-purple-600/20 text-purple-400 border-l-4 border-purple-500'
                          : 'bg-purple-50 text-purple-600 border-l-4 border-purple-500'
                        : darkMode
                          ? 'text-gray-300 hover:bg-gray-600'
                          : 'text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    {t('admin.stats.allUsers')}
                  </button>
                  {uniqueUsers.map(user => (
                    <button
                      key={user.id}
                      type="button"
                      onClick={() => {
                        setSelectedUser(user.id);
                        setIsUserDropdownOpen(false);
                      }}
                      className={`w-full text-left px-4 py-3 font-light tracking-wide transition-all duration-200 ${
                        selectedUser === user.id
                          ? darkMode
                            ? 'bg-purple-600/20 text-purple-400 border-l-4 border-purple-500'
                            : 'bg-purple-50 text-purple-600 border-l-4 border-purple-500'
                          : darkMode
                            ? 'text-gray-300 hover:bg-gray-600'
                            : 'text-gray-700 hover:bg-gray-50'
                      }`}
                    >
                      {user.firstName} {user.lastName}
                    </button>
                  ))}
                </div>
              )}
            </div>

            {/* Filtro por estado - Custom Dropdown */}
            <div className="relative" ref={statusDropdownRef}>
              <button
                type="button"
                onClick={() => setIsStatusDropdownOpen(!isStatusDropdownOpen)}
                className={`w-full px-4 py-2 border rounded-lg font-light tracking-wide focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 flex items-center justify-between ${
                  darkMode
                    ? 'border-gray-600 bg-gray-700 text-white hover:bg-gray-600'
                    : 'border-gray-300 bg-white text-gray-900 hover:bg-gray-50'
                } cursor-pointer`}
              >
                <span>
                  {selectedStatus === 'all'
                    ? t('admin.stats.allStatuses')
                    : getStatusText(selectedStatus)
                  }
                </span>
                <ChevronDown
                  size={16}
                  className={`transition-transform duration-200 ${isStatusDropdownOpen ? 'rotate-180' : ''} ${
                    darkMode ? 'text-gray-400' : 'text-gray-500'
                  }`}
                />
              </button>

              {isStatusDropdownOpen && (
                <div className={`absolute top-full left-0 right-0 mt-1 border rounded-lg shadow-lg z-50 overflow-hidden ${
                  darkMode
                    ? 'border-gray-600 bg-gray-700'
                    : 'border-gray-300 bg-white'
                }`}>
                  <button
                    type="button"
                    onClick={() => {
                      setSelectedStatus('all');
                      setIsStatusDropdownOpen(false);
                    }}
                    className={`w-full text-left px-4 py-3 font-light tracking-wide transition-all duration-200 ${
                      selectedStatus === 'all'
                        ? darkMode
                          ? 'bg-purple-600/20 text-purple-400 border-l-4 border-purple-500'
                          : 'bg-purple-50 text-purple-600 border-l-4 border-purple-500'
                        : darkMode
                          ? 'text-gray-300 hover:bg-gray-600'
                          : 'text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    {t('admin.stats.allStatuses')}
                  </button>
                  {uniqueStatuses.map(status => (
                    <button
                      key={status}
                      type="button"
                      onClick={() => {
                        setSelectedStatus(status);
                        setIsStatusDropdownOpen(false);
                      }}
                      className={`w-full text-left px-4 py-3 font-light tracking-wide transition-all duration-200 ${
                        selectedStatus === status
                          ? darkMode
                            ? 'bg-purple-600/20 text-purple-400 border-l-4 border-purple-500'
                            : 'bg-purple-50 text-purple-600 border-l-4 border-purple-500'
                          : darkMode
                            ? 'text-gray-300 hover:bg-gray-600'
                            : 'text-gray-700 hover:bg-gray-50'
                      }`}
                    >
                      {getStatusText(status)}
                    </button>
                  ))}
                </div>
              )}
            </div>

            {/* Filtro por algoritmo - Custom Dropdown */}
            <div className="relative" ref={algorithmDropdownRef}>
              <button
                type="button"
                onClick={() => setIsAlgorithmDropdownOpen(!isAlgorithmDropdownOpen)}
                className={`w-full px-4 py-2 border rounded-lg font-light tracking-wide focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 flex items-center justify-between ${
                  darkMode
                    ? 'border-gray-600 bg-gray-700 text-white hover:bg-gray-600'
                    : 'border-gray-300 bg-white text-gray-900 hover:bg-gray-50'
                } cursor-pointer`}
              >
                <span>
                  {selectedAlgorithm === 'all'
                    ? t('admin.stats.allAlgorithms')
                    : selectedAlgorithm
                  }
                </span>
                <ChevronDown
                  size={16}
                  className={`transition-transform duration-200 ${isAlgorithmDropdownOpen ? 'rotate-180' : ''} ${
                    darkMode ? 'text-gray-400' : 'text-gray-500'
                  }`}
                />
              </button>

              {isAlgorithmDropdownOpen && (
                <div className={`absolute top-full left-0 right-0 mt-1 border rounded-lg shadow-lg z-50 overflow-hidden ${
                  darkMode
                    ? 'border-gray-600 bg-gray-700'
                    : 'border-gray-300 bg-white'
                }`}>
                  <button
                    type="button"
                    onClick={() => {
                      setSelectedAlgorithm('all');
                      setIsAlgorithmDropdownOpen(false);
                    }}
                    className={`w-full text-left px-4 py-3 font-light tracking-wide transition-all duration-200 ${
                      selectedAlgorithm === 'all'
                        ? darkMode
                          ? 'bg-purple-600/20 text-purple-400 border-l-4 border-purple-500'
                          : 'bg-purple-50 text-purple-600 border-l-4 border-purple-500'
                        : darkMode
                          ? 'text-gray-300 hover:bg-gray-600'
                          : 'text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    {t('admin.stats.allAlgorithms')}
                  </button>
                  {uniqueAlgorithms.map(algorithm => (
                    <button
                      key={algorithm}
                      type="button"
                      onClick={() => {
                        setSelectedAlgorithm(algorithm);
                        setIsAlgorithmDropdownOpen(false);
                      }}
                      className={`w-full text-left px-4 py-3 font-light tracking-wide transition-all duration-200 ${
                        selectedAlgorithm === algorithm
                          ? darkMode
                            ? 'bg-purple-600/20 text-purple-400 border-l-4 border-purple-500'
                            : 'bg-purple-50 text-purple-600 border-l-4 border-purple-500'
                          : darkMode
                            ? 'text-gray-300 hover:bg-gray-600'
                            : 'text-gray-700 hover:bg-gray-50'
                      }`}
                    >
                      {algorithm}
                    </button>
                  ))}
                </div>
              )}
            </div>

            {/* Botón limpiar filtros */}
            <button
              onClick={() => {
                setSearchTerm('');
                setSelectedUser('all');
                setSelectedStatus('all');
                setSelectedAlgorithm('all');
              }}
              className="px-4 py-2 rounded-lg bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-light tracking-wide transition-colors duration-200"
            >
              {t('admin.stats.clearFilters')}
            </button>
          </div>
        </div>

        {/* Lista de llaves */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-light tracking-wide text-gray-900 dark:text-white">
              {t('admin.stats.systemKeys')} ({filteredKeys.length})
            </h2>
          </div>

          {/* Controles de paginación estilo Gmail - ARRIBA */}
          {filteredKeys.length > 0 && totalPages > 1 && (
            <div className="mb-6">
              <PaginationControls />
            </div>
          )}

          {filteredKeys.length === 0 ? (
            <div className="text-center py-12">
              <Key size={48} className="mx-auto text-gray-400 mb-4" />
              <p className="text-lg font-light tracking-wide text-gray-600 dark:text-gray-400">
                {t('admin.stats.noKeysFound')}
              </p>
              <p className="text-sm font-light tracking-wide text-gray-500 dark:text-gray-500">
                {t('admin.stats.adjustFilters')}
              </p>
            </div>
          ) : (
            <>
              <div className={`grid gap-4 transition-all duration-300 ${
                isAnimating
                  ? animationDirection === 'next'
                    ? 'transform translate-x-4 opacity-70'
                    : 'transform -translate-x-4 opacity-70'
                  : 'transform translate-x-0 opacity-100'
              }`}>
                {currentKeys.map((key) => (
                <div
                  key={`${key.userId}-${key.id || key.name}`}
                  className="p-4 rounded-xl border shadow-sm bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 hover:shadow-md transition-all duration-300"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <Key size={18} className="text-purple-500" />
                        <h3 className="text-lg font-light tracking-wide text-gray-900 dark:text-white">
                          {key.name || t('admin.stats.noName')}
                        </h3>
                        <div className="flex items-center gap-1">
                          {getStatusIcon(key.status)}
                          <span className="text-sm font-light tracking-wide text-gray-600 dark:text-gray-400">
                            {getStatusText(key.status)}
                          </span>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <span className="text-gray-600 dark:text-gray-400">{t('admin.stats.user')}: </span>
                          <span className="font-medium text-gray-900 dark:text-white">
                            {key.userName}
                          </span>
                        </div>
                        <div>
                          <span className="text-gray-600 dark:text-gray-400">{t('admin.stats.algorithm')}: </span>
                          <span className="font-medium text-gray-900 dark:text-white">
                            {key.algorithm || 'N/A'}
                          </span>
                        </div>
                        <div>
                          <span className="text-gray-600 dark:text-gray-400">{t('admin.stats.bytes')}: </span>
                          <span className="font-medium text-gray-900 dark:text-white">
                            {key.numBytes || key.num_bytes || 'N/A'}
                          </span>
                        </div>
                        <div>
                          <span className="text-gray-600 dark:text-gray-400">{t('admin.stats.created')}: </span>
                          <span className="font-medium text-gray-900 dark:text-white">
                            {key.createdAt ? new Date(key.createdAt).toLocaleDateString() : 'N/A'}
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-2 ml-4">
                      <button
                        onClick={() => handleViewKeyDetail(key)}
                        className="p-2 border rounded-lg transition-all duration-200 transform border-gray-200 bg-gray-50 text-blue-500 hover:bg-blue-50 hover:border-blue-300 hover:text-blue-600 hover:scale-110 dark:border-gray-600 dark:bg-gray-700 dark:text-blue-400 dark:hover:bg-blue-900/20 dark:hover:border-blue-500"
                        title={t('admin.stats.viewKeyDetail')}
                      >
                        <Eye size={18} />
                      </button>
                      <button
                        onClick={() => {
                          const user = usersWithKeys.find(u => u.id === key.userId);
                          if (user) handleViewUserKeys(user);
                        }}
                        className="p-2 border rounded-lg transition-all duration-200 transform border-gray-200 bg-gray-50 text-green-500 hover:bg-green-50 hover:border-green-300 hover:text-green-600 hover:scale-110 dark:border-gray-600 dark:bg-gray-700 dark:text-green-400 dark:hover:bg-green-900/20 dark:hover:border-green-500"
                        title={t('admin.stats.viewUserKeys')}
                      >
                        <User size={18} />
                      </button>
                    </div>
                  </div>
                </div>
                ))}
              </div>


            </>
          )}
        </div>

        {/* Resumen por usuario - Compacto */}
        <div className="space-y-3">
          <h2 className="text-lg font-light tracking-wide text-gray-900 dark:text-white">
            {t('admin.stats.userSummary')}
          </h2>

          <div className="grid gap-3 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
            {uniqueUsers.map((user) => (
              <div
                key={user.id}
                className="p-3 sm:p-4 rounded-xl border shadow-sm bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 hover:shadow-md transition-all duration-300"
              >
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 mb-3">
                  <div className="flex items-center gap-2 min-w-0 flex-1">
                    <div className="p-1.5 rounded-lg bg-indigo-50 dark:bg-indigo-900/20 flex-shrink-0">
                      <User size={14} className="text-indigo-500" />
                    </div>
                    <div className="min-w-0 flex-1">
                      <h3 className="text-sm font-light tracking-wide text-gray-900 dark:text-white truncate">
                        {user.firstName} {user.lastName}
                      </h3>
                      <p className="text-xs font-light tracking-wide text-gray-600 dark:text-gray-400 truncate">
                        {user.email}
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={() => handleViewUserKeys(user)}
                    className="p-1.5 border rounded-lg transition-all duration-200 transform border-gray-200 bg-gray-50 text-purple-500 hover:bg-purple-50 hover:border-purple-300 hover:text-purple-600 hover:scale-110 dark:border-gray-600 dark:bg-gray-700 dark:text-purple-400 dark:hover:bg-purple-900/20 dark:hover:border-purple-500 self-start sm:self-center flex-shrink-0"
                    title="Ver llaves del usuario"
                  >
                    <Eye size={14} />
                  </button>
                </div>

                <div className="grid grid-cols-3 gap-2 text-xs">
                  <div className="text-center p-2 rounded-lg bg-blue-50 dark:bg-blue-900/20">
                    <p className="text-base sm:text-lg font-light tracking-wide text-blue-600 dark:text-blue-400">
                      {user.keys?.length || 0}
                    </p>
                    <p className="text-xs font-light tracking-wide text-blue-600 dark:text-blue-400">
                      {t('admin.stats.total')}
                    </p>
                  </div>
                  <div className="text-center p-2 rounded-lg bg-green-50 dark:bg-green-900/20">
                    <p className="text-base sm:text-lg font-light tracking-wide text-green-600 dark:text-green-400">
                      {user.keys?.filter(k => k.status === 'uploaded_to_ctm').length || 0}
                    </p>
                    <p className="text-xs font-light tracking-wide text-green-600 dark:text-green-400">
                      {t('admin.stats.active')}
                    </p>
                  </div>
                  <div className="text-center p-2 rounded-lg bg-red-50 dark:bg-red-900/20">
                    <p className="text-base sm:text-lg font-light tracking-wide text-red-600 dark:text-red-400">
                      {user.keys?.filter(k => k.status === 'failed').length || 0}
                    </p>
                    <p className="text-xs font-light tracking-wide text-red-600 dark:text-red-400">
                      {t('admin.stats.failed')}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Modales */}
      <UserKeysModal
        isOpen={showUserKeysModal}
        onClose={() => setShowUserKeysModal(false)}
        onReopen={() => setShowUserKeysModal(true)}
        user={selectedUserForKeys}
        userKeys={userKeys}
        isLoading={false}
        darkMode={darkMode}
      />

      <KeyDetailModal
        isOpen={showKeyDetailModal}
        onClose={() => setShowKeyDetailModal(false)}
        keyData={selectedKey}
        darkMode={darkMode}
      />
    </>
  );
};

AdminKeysSection.propTypes = {
  usersWithKeys: PropTypes.array.isRequired,
  isLoading: PropTypes.bool.isRequired,
  darkMode: PropTypes.bool.isRequired
};

export default AdminKeysSection;
