import { useState, useEffect, useRef } from 'react';
import { Upload, Key, Settings, Hash, X, ChevronDown, CheckCircle } from 'lucide-react';
import PropTypes from 'prop-types';
import { Modal } from '../../common';
import { useLanguage } from '../../../hooks';

/**
 * Modal para subir nueva llave a CTM
 */
const KeyUploadModal = ({ isOpen, onClose, onUpload, darkMode }) => {
  const [formData, setFormData] = useState({
    key_name: '',
    algorithm: 'AES',
    num_bytes: 32,
    exportable: true
  });
  const [isUploading, setIsUploading] = useState(false);
  const [validationError, setValidationError] = useState('');
  const [showSuccessNotification, setShowSuccessNotification] = useState(false);
  const [uploadedKeyName, setUploadedKeyName] = useState('');
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isBytesDropdownOpen, setIsBytesDropdownOpen] = useState(false);
  const dropdownRef = useRef(null);
  const bytesDropdownRef = useRef(null);
  const { t } = useLanguage();

  // Cerrar dropdowns al hacer click fuera
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsDropdownOpen(false);
      }
      if (bytesDropdownRef.current && !bytesDropdownRef.current.contains(event.target)) {
        setIsBytesDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Valores permitidos por algoritmo según CTM
  const algorithmLimits = {
    AES: { values: [16, 24, 32], label: 'AES' },
    ARIA: { values: [16, 24, 32], label: 'ARIA' },
    RSA: { values: [64, 128, 256, 384, 512], label: 'RSA' },
    'HMAC-SHA1': { values: [16, 24, 32], label: 'HMAC-SHA1' },
    'HMAC-SHA256': { values: [16, 24, 32, 64], label: 'HMAC-SHA256' },
    'HMAC-SHA384': { values: [24, 36, 48], label: 'HMAC-SHA384' },
    'HMAC-SHA512': { values: [32, 48, 64], label: 'HMAC-SHA512' }
  };

  const handleChange = (field, value) => {
    if (field === 'algorithm') {
      // Cuando cambia el algoritmo, usar el valor por defecto o el primer valor válido
      const validValues = algorithmLimits[value].values;
      const currentBytes = formData.num_bytes;
      let newBytes;

      if (value === 'RSA') {
        // Para RSA, usar 128 bytes (1024 bits) como default
        newBytes = 128;
      } else {
        newBytes = validValues.includes(currentBytes) ? currentBytes : validValues[0];
      }

      setFormData(prev => ({
        ...prev,
        [field]: value,
        num_bytes: newBytes
      }));
      setValidationError('');
    } else if (field === 'num_bytes') {
      // Validar que el valor esté en la lista permitida
      const validValues = algorithmLimits[formData.algorithm].values;
      if (!validValues.includes(value)) {
        setValidationError(`${formData.algorithm} solo permite: ${validValues.join(', ')} bytes`);
        return;
      } else {
        setValidationError('');
      }
      setFormData(prev => ({ ...prev, [field]: value }));
    } else {
      setFormData(prev => ({ ...prev, [field]: value }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validación final antes de enviar
    const validValues = algorithmLimits[formData.algorithm].values;
    if (!validValues.includes(formData.num_bytes)) {
      setValidationError(`${formData.algorithm} solo permite: ${validValues.join(', ')} bytes`);
      return;
    }

    setIsUploading(true);
    setValidationError('');
    try {
      await onUpload(formData);

      // Mostrar notificación de éxito
      setUploadedKeyName(formData.key_name);
      setShowSuccessNotification(true);

      // Auto-ocultar notificación después de 4 segundos
      setTimeout(() => {
        setShowSuccessNotification(false);
      }, 4000);

      // Reset form
      setFormData({
        key_name: '',
        algorithm: 'AES',
        num_bytes: 32,
        exportable: true
      });
      setValidationError('');
    } finally {
      setIsUploading(false);
    }
  };

  const algorithmOptions = [
    { value: 'AES', label: 'AES' },
    { value: 'ARIA', label: 'ARIA' },
    { value: 'RSA', label: 'RSA' },
    { value: 'HMAC-SHA1', label: 'HMAC-SHA1' },
    { value: 'HMAC-SHA256', label: 'HMAC-SHA256' },
    { value: 'HMAC-SHA384', label: 'HMAC-SHA384' },
    { value: 'HMAC-SHA512', label: 'HMAC-SHA512' }
  ];

  return (
    <>
      <Modal
        isOpen={isOpen}
        onClose={onClose}
      title={
        <div className="flex items-center gap-2 sm:gap-3">
          <Upload size={20} className="text-blue-500 sm:w-6 sm:h-6" />
          <div>
            <h3 className="text-lg sm:text-2xl font-light tracking-wide leading-relaxed text-gray-900 dark:text-white">
              {t('keys.upload.title')}
            </h3>
            <p className="text-xs sm:text-sm font-light tracking-wide text-gray-600 dark:text-gray-400">
              {t('keys.upload.subtitle')}
            </p>
          </div>
        </div>
      }
      maxWidth="max-w-2xl"
      darkMode={darkMode}
    >

      <div className="space-y-3 sm:space-y-4">
        {/* Información de la Llave */}
        <div className={`p-3 sm:p-4 rounded-xl border shadow-sm ${
          darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
        }`}>
          <h4 className="text-sm sm:text-base font-light tracking-wide mb-3 flex items-center gap-2 text-gray-900 dark:text-white">
            <Key size={16} className="text-orange-500 sm:w-5 sm:h-5" />
            {t('keys.upload.keyInfo')}
          </h4>

          <div>
            <label className="block text-xs sm:text-sm font-light tracking-wide text-gray-600 dark:text-gray-400 mb-2">
              {t('keys.upload.keyName')} <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              value={formData.key_name}
              onChange={(e) => handleChange('key_name', e.target.value)}
              placeholder={t('keys.upload.keyNamePlaceholder')}
              required
              disabled={isUploading}
              className={`w-full px-3 sm:px-4 py-2.5 sm:py-3 border rounded-lg font-light tracking-wide focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors duration-200 text-sm sm:text-base ${
                darkMode
                  ? 'border-gray-600 bg-gray-700 text-white placeholder-gray-400'
                  : 'border-gray-300 bg-white text-gray-900 placeholder-gray-500'
              } ${isUploading ? 'opacity-50 cursor-not-allowed' : ''}`}
            />
            <p className="text-xs font-light tracking-wide text-gray-500 dark:text-gray-400 mt-2">
              {t('keys.upload.keyNameHelp')}
            </p>
          </div>
        </div>


        {/* Configuración Técnica */}
        <div className={`p-4 rounded-xl border shadow-sm ${
          darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
        }`}>
          <h4 className="text-base font-light tracking-wide mb-3 flex items-center gap-2 text-gray-900 dark:text-white">
            <Settings size={18} className="text-purple-500" />
            {t('keys.upload.technicalConfig')}
          </h4>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-light tracking-wide text-gray-600 dark:text-gray-400 mb-2">
                {t('keys.upload.algorithm')}
              </label>
              <div className="relative" ref={dropdownRef}>
                <button
                  type="button"
                  onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                  disabled={isUploading}
                  className={`w-full px-4 py-3 border rounded-lg font-light tracking-wide focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 flex items-center justify-between ${
                    darkMode
                      ? 'border-gray-600 bg-gray-700 text-white hover:bg-gray-600'
                      : 'border-gray-300 bg-white text-gray-900 hover:bg-gray-50'
                  } ${isUploading ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
                >
                  <span>{formData.algorithm}</span>
                  <ChevronDown
                    size={16}
                    className={`transition-transform duration-200 ${isDropdownOpen ? 'rotate-180' : ''} ${
                      darkMode ? 'text-gray-400' : 'text-gray-500'
                    }`}
                  />
                </button>

                {isDropdownOpen && (
                  <div className={`absolute top-full left-0 right-0 mt-1 border rounded-lg shadow-lg z-50 overflow-hidden ${
                    darkMode
                      ? 'border-gray-600 bg-gray-700'
                      : 'border-gray-300 bg-white'
                  }`}>
                    {algorithmOptions.map(option => (
                      <button
                        key={option.value}
                        type="button"
                        onClick={() => {
                          handleChange('algorithm', option.value);
                          setIsDropdownOpen(false);
                        }}
                        className={`w-full px-4 py-3 text-left font-light tracking-wide transition-colors duration-200 flex items-center justify-between ${
                          formData.algorithm === option.value
                            ? darkMode
                              ? 'bg-purple-900/30 text-purple-300'
                              : 'bg-purple-50 text-purple-700'
                            : darkMode
                              ? 'text-white hover:bg-gray-600'
                              : 'text-gray-900 hover:bg-gray-50'
                        }`}
                      >
                        <span>{option.label}</span>
                        <span className="text-xs opacity-70">
                          {algorithmLimits[option.value].values.join(', ')} bytes
                        </span>
                      </button>
                    ))}
                  </div>
                )}
              </div>
              <p className="text-xs font-light tracking-wide text-gray-500 dark:text-gray-400 mt-2">
                <span className="font-medium">{formData.algorithm}</span> ➔ {t('keys.upload.allowedValues', { values: algorithmLimits[formData.algorithm].values.join(', ') })}
              </p>
            </div>

            <div>
              <label className="block text-sm font-light tracking-wide text-gray-600 dark:text-gray-400 mb-2">
                {t('keys.upload.keySize')}
              </label>
              <div className="relative" ref={bytesDropdownRef}>
                <button
                  type="button"
                  onClick={() => setIsBytesDropdownOpen(!isBytesDropdownOpen)}
                  disabled={isUploading}
                  className={`w-full px-4 py-3 border rounded-lg font-light tracking-wide focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 flex items-center justify-between ${
                    validationError
                      ? darkMode
                        ? 'border-red-500 bg-gray-700 text-white hover:bg-gray-600'
                        : 'border-red-500 bg-white text-gray-900 hover:bg-gray-50'
                      : darkMode
                        ? 'border-gray-600 bg-gray-700 text-white hover:bg-gray-600'
                        : 'border-gray-300 bg-white text-gray-900 hover:bg-gray-50'
                  } ${isUploading ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
                >
                  <span>{formData.num_bytes} bytes</span>
                  <ChevronDown
                    size={16}
                    className={`transition-transform duration-200 ${
                      isBytesDropdownOpen ? 'rotate-180' : ''
                    }`}
                  />
                </button>
                {isBytesDropdownOpen && (
                  <div className={`absolute z-10 w-full mt-1 border rounded-lg shadow-lg ${
                    darkMode ? 'bg-gray-700 border-gray-600' : 'bg-white border-gray-300'
                  }`}>
                    {algorithmLimits[formData.algorithm].values.map((bytes) => (
                      <button
                        key={bytes}
                        type="button"
                        onClick={() => {
                          handleChange('num_bytes', bytes);
                          setIsBytesDropdownOpen(false);
                        }}
                        className={`w-full px-4 py-3 text-left font-light tracking-wide transition-colors duration-200 flex items-center justify-between ${
                          formData.num_bytes === bytes
                            ? darkMode
                              ? 'bg-purple-900/30 text-purple-300'
                              : 'bg-purple-50 text-purple-700'
                            : darkMode
                              ? 'text-white hover:bg-gray-600'
                              : 'text-gray-900 hover:bg-gray-50'
                        }`}
                      >
                        <span>{bytes} bytes</span>
                        {formData.algorithm === 'RSA' && (
                          <span className="text-xs opacity-70">
                            {bytes * 8} bits
                          </span>
                        )}
                      </button>
                    ))}
                  </div>
                )}
              </div>
              {validationError ? (
                <p className="text-xs font-light tracking-wide text-red-500 mt-2 flex items-center gap-1">
                  <span className="text-red-500">⚠️</span>
                  {validationError}
                </p>
              ) : (
                <p className="text-xs font-light tracking-wide text-gray-500 dark:text-gray-400 mt-2">
                  {t('keys.upload.selectAllowedValue', { algorithm: formData.algorithm })}
                </p>
              )}
            </div>

            <div className={`flex items-center gap-3 p-3 rounded-lg border ${
              darkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-200'
            }`}>
              <input
                type="checkbox"
                id="exportable"
                checked={formData.exportable}
                onChange={(e) => handleChange('exportable', e.target.checked)}
                disabled={isUploading}
                className="w-4 h-4 text-purple-600 bg-white border-gray-300 rounded focus:ring-2 focus:ring-purple-500 transition-colors duration-200"
              />
              <label htmlFor="exportable" className="text-sm font-light tracking-wide text-gray-700 dark:text-gray-300 cursor-pointer">
                {t('keys.upload.exportable')}
              </label>
            </div>
          </div>
        </div>

        {/* Generación Automática */}
        <div className={`p-4 rounded-xl border shadow-sm ${
          darkMode ? 'bg-blue-900/10 border-blue-700' : 'bg-blue-50 border-blue-200'
        }`}>
          <div className="flex items-start gap-3">
            <Hash size={18} className="text-blue-500 flex-shrink-0" />
            <div className="flex-1">
              <h4 className="text-base font-light tracking-wide text-blue-700 dark:text-blue-300 mb-2">
                {t('keys.upload.autoGeneration')}
              </h4>
              <p className="text-sm font-light tracking-wide text-blue-600 dark:text-blue-400">
                {t('keys.upload.autoGenerationDesc')}
              </p>
            </div>
          </div>
        </div>

        {/* Botones */}
        <form onSubmit={handleSubmit}>
          <div className="flex justify-center gap-3 mt-6 pt-4 border-t border-gray-200 dark:border-gray-600">
            {isUploading ? (
              <button
                disabled
                className="px-8 py-3 rounded-xl bg-blue-600 text-white opacity-50 cursor-not-allowed flex items-center justify-center gap-2 font-light tracking-wide shadow-lg"
              >
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                {t('keys.upload.uploading')}
              </button>
            ) : (
              <>
                <button
                  type="button"
                  onClick={onClose}
                  className="px-8 py-3 rounded-xl bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-light tracking-wide shadow-sm hover:shadow-md transform hover:scale-105 transition-all duration-300"
                >
                  {t('common.cancel')}
                </button>
                <button
                  type="submit"
                  disabled={!formData.key_name || validationError}
                  className={`px-8 py-3 rounded-xl bg-blue-600 hover:bg-blue-700 text-white font-light tracking-wide shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 flex items-center justify-center gap-2 ${
                    (!formData.key_name || validationError) ? 'opacity-50 cursor-not-allowed' : ''
                  }`}
                >
                  <Upload size={16} />
                  {t('keys.upload.uploadButton')}
                </button>
              </>
            )}
          </div>
        </form>
      </div>
    </Modal>

    {/* 🎉 Notificación de éxito elegante */}
    {showSuccessNotification && (
      <div className="fixed bottom-4 sm:bottom-8 left-1/2 transform -translate-x-1/2 z-50 px-4">
        <div className="bg-green-500 text-white px-4 sm:px-8 py-3 sm:py-4 rounded-xl shadow-2xl flex items-center gap-3 animate-bounce max-w-sm sm:max-w-md">
          <div className="w-5 h-5 sm:w-6 sm:h-6 bg-white rounded-full flex items-center justify-center flex-shrink-0">
            <CheckCircle size={16} className="text-green-500" />
          </div>
          <div className="min-w-0">
            <p className="font-light tracking-wide text-sm sm:text-base">
              {t('keys.upload.successTitle')}
            </p>
            <p className="text-xs sm:text-sm opacity-90 truncate">
              {t('keys.upload.successMessage', { keyName: uploadedKeyName })}
            </p>
          </div>
        </div>
      </div>
    )}
    </>
  );
};

KeyUploadModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onUpload: PropTypes.func.isRequired,
  darkMode: PropTypes.bool.isRequired
};

export default KeyUploadModal;
