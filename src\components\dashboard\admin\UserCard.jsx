import { Edit, Trash2, Key, User, Mail, Building, Eye } from 'lucide-react';
import PropTypes from 'prop-types';
import { Button } from '../../common';
import { useLanguage } from '../../../hooks';

/**
 * Componente para mostrar información de un usuario con diseño moderno hermoso minimalista y consistente
 */
const UserCard = ({ user, darkMode, onEdit, onDelete, onViewDetail }) => {
  const { t } = useLanguage();

  return (
    <div className="p-4 rounded-xl border shadow-sm bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 hover:shadow-md transition-all duration-300">
      {/* Header con nombre, badges y botones */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center gap-3">
          <User size={18} className="text-blue-500" />
          <div>
            <div className="flex items-center gap-3 mb-2">
              <h3 className="text-base font-light tracking-wide text-gray-900 dark:text-white">
                {user.firstName} {user.lastName}
              </h3>

              {/* Badge de estado - Estilo limpio y minimalista */}
              <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border ${
                user.isActive
                  ? 'border-green-200 bg-green-50 text-green-700 dark:border-green-700 dark:bg-green-900/20 dark:text-green-300'
                  : 'border-red-200 bg-red-50 text-red-700 dark:border-red-700 dark:bg-red-900/20 dark:text-red-300'
              }`}>
                {user.isActive ? t('admin.users.active') : t('admin.users.inactive')}
              </span>

              {/* Badge de rol - Estilo limpio y minimalista */}
              <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border ${
                user.role === 'ADMIN'
                  ? 'border-purple-200 bg-purple-50 text-purple-700 dark:border-purple-700 dark:bg-purple-900/20 dark:text-purple-300'
                  : 'border-blue-200 bg-blue-50 text-blue-700 dark:border-blue-700 dark:bg-blue-900/20 dark:text-blue-300'
              }`}>
                {user.role === 'ADMIN' ? t('admin.users.admin') : t('admin.users.user')}
              </span>
            </div>
          </div>
        </div>

        {/* Botones de acción con estilo limpio y minimalista */}
        <div className="flex flex-row gap-3">
          <button
            onClick={onEdit}
            className="p-2 border rounded-lg transition-all duration-200 transform border-gray-200 bg-gray-50 text-blue-500 hover:bg-blue-50 hover:border-blue-300 hover:text-blue-600 hover:scale-110 dark:border-gray-600 dark:bg-gray-700 dark:text-blue-400 dark:hover:bg-blue-900/20 dark:hover:border-blue-500"
            title={t('admin.users.editUser')}
          >
            <Edit size={18} />
          </button>
          <button
            onClick={onViewDetail}
            className="p-2 border rounded-lg transition-all duration-200 transform border-gray-200 bg-gray-50 text-green-500 hover:bg-green-50 hover:border-green-300 hover:text-green-600 hover:scale-110 dark:border-gray-600 dark:bg-gray-700 dark:text-green-400 dark:hover:bg-green-900/20 dark:hover:border-green-500"
            title={t('admin.users.viewDetail')}
          >
            <Eye size={18} />
          </button>
          <button
            onClick={onDelete}
            className="p-2 border rounded-lg transition-all duration-200 transform border-gray-200 bg-gray-50 text-red-500 hover:bg-red-50 hover:border-red-300 hover:text-red-600 hover:scale-110 dark:border-gray-600 dark:bg-gray-700 dark:text-red-400 dark:hover:bg-red-900/20 dark:hover:border-red-500"
            title={t('admin.users.deleteUser')}
          >
            <Trash2 size={18} />
          </button>
        </div>
      </div>

      {/* Información básica - Más compacta */}
      <div className="space-y-2 mb-4">
        <div className="flex items-center gap-2">
          <Mail size={14} className="text-gray-500 dark:text-gray-400" />
          <span className="text-sm font-light tracking-wide text-gray-600 dark:text-gray-300">
            {user.email}
          </span>
        </div>

        {user.company && (
          <div className="flex items-center gap-2">
            <Building size={14} className="text-gray-500 dark:text-gray-400" />
            <span className="text-sm font-light tracking-wide text-gray-600 dark:text-gray-300">
              {user.company}
            </span>
          </div>
        )}
      </div>




    </div>
  );
};

UserCard.propTypes = {
  user: PropTypes.object.isRequired,
  darkMode: PropTypes.bool.isRequired,
  onEdit: PropTypes.func.isRequired,
  onDelete: PropTypes.func.isRequired,
  onViewDetail: PropTypes.func.isRequired
};

export default UserCard;
