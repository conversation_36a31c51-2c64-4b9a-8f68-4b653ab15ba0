import { User, Mail, Building, Shield, Server, Eye } from 'lucide-react';
import PropTypes from 'prop-types';
import { Modal } from '../../common';
import { useLanguage } from '../../../hooks';

/**
 * Modal hermoso para mostrar detalles completos del usuario
 * Diseño moderno hermoso minimalista y con consistencia
 */
const UserDetailModal = ({ isOpen, onClose, user, darkMode }) => {
  const { t } = useLanguage();

  if (!user) return null;

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Detalle de Usuario"
      maxWidth="max-w-4xl"
      darkMode={darkMode}
      className="max-h-[90vh] overflow-y-auto"
    >
      <div className="space-y-6">
        {/* Información Personal */}
        <div className="p-4 rounded-xl border shadow-sm bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
          <h3 className="text-base font-light tracking-wide mb-4 flex items-center gap-3 text-blue-600 dark:text-blue-400">
            <div className="p-1.5 rounded-lg bg-blue-50 dark:bg-blue-900/20">
              <User size={16} className="text-blue-500" />
            </div>
            Información Personal
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <Mail size={14} className="text-gray-500 dark:text-gray-400" />
                <div>
                  <span className="text-xs font-light tracking-wide text-gray-500 dark:text-gray-400 block">Nombre Completo</span>
                  <span className="text-sm font-light tracking-wide text-gray-900 dark:text-white">
                    {user.firstName} {user.lastName}
                  </span>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <Mail size={14} className="text-gray-500 dark:text-gray-400" />
                <div>
                  <span className="text-xs font-light tracking-wide text-gray-500 dark:text-gray-400 block">Email</span>
                  <span className="text-sm font-light tracking-wide text-gray-900 dark:text-white">
                    {user.email}
                  </span>
                </div>
              </div>

              {user.company && (
                <div className="flex items-center gap-3">
                  <Building size={14} className="text-gray-500 dark:text-gray-400" />
                  <div>
                    <span className="text-xs font-light tracking-wide text-gray-500 dark:text-gray-400 block">Empresa</span>
                    <span className="text-sm font-light tracking-wide text-gray-900 dark:text-white">
                      {user.company}
                    </span>
                  </div>
                </div>
              )}

              <div className="flex items-center gap-3">
                <Shield size={14} className="text-gray-500 dark:text-gray-400" />
                <div>
                  <span className="text-xs font-light tracking-wide text-gray-500 dark:text-gray-400 block">Rol</span>
                  <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border ${
                    user.role === 'ADMIN'
                      ? 'border-purple-200 bg-purple-50 text-purple-700 dark:border-purple-700 dark:bg-purple-900/20 dark:text-purple-300'
                      : 'border-blue-200 bg-blue-50 text-blue-700 dark:border-blue-700 dark:bg-blue-900/20 dark:text-blue-300'
                  }`}>
                    {user.role === 'ADMIN' ? 'Administrador' : 'Usuario'}
                  </span>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <Eye size={14} className="text-gray-500 dark:text-gray-400" />
                <div>
                  <span className="text-xs font-light tracking-wide text-gray-500 dark:text-gray-400 block">Estado</span>
                  <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border ${
                    user.isActive
                      ? 'border-green-200 bg-green-50 text-green-700 dark:border-green-700 dark:bg-green-900/20 dark:text-green-300'
                      : 'border-red-200 bg-red-50 text-red-700 dark:border-red-700 dark:bg-red-900/20 dark:text-red-300'
                  }`}>
                    {user.isActive ? 'Activo' : 'Inactivo'}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* CipherTrust Manager */}
        <div className="p-4 rounded-xl border shadow-sm bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
          <h3 className="text-base font-light tracking-wide mb-4 flex items-center gap-3 text-orange-600 dark:text-orange-400">
            <div className="p-1.5 rounded-lg bg-orange-50 dark:bg-orange-900/20">
              <Shield size={16} className="text-orange-500" />
            </div>
            CipherTrust Manager
          </h3>

          <div className="space-y-3">
            <div className="flex items-center gap-3">
              <Server size={14} className="text-gray-500 dark:text-gray-400" />
              <div>
                <span className="text-xs font-light tracking-wide text-gray-500 dark:text-gray-400 block">IP Address</span>
                <span className="text-sm font-light tracking-wide text-gray-900 dark:text-white">
                  {user.ctmIpAddress || 'https://ctm.example.com:443'}
                </span>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <User size={14} className="text-gray-500 dark:text-gray-400" />
              <div>
                <span className="text-xs font-light tracking-wide text-gray-500 dark:text-gray-400 block">Username</span>
                <span className="text-sm font-light tracking-wide text-gray-900 dark:text-white">
                  {user.ctmUsername || 'N/A'}
                </span>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <Eye size={14} className="text-gray-500 dark:text-gray-400" />
              <div>
                <span className="text-xs font-light tracking-wide text-gray-500 dark:text-gray-400 block">Password</span>
                <span className="text-sm font-light tracking-wide text-gray-900 dark:text-white">
                  ••••••••
                </span>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <Building size={14} className="text-gray-500 dark:text-gray-400" />
              <div>
                <span className="text-xs font-light tracking-wide text-gray-500 dark:text-gray-400 block">Domain</span>
                <span className="text-sm font-light tracking-wide text-gray-900 dark:text-white">
                  {user.ctmDomain || 'root'}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* SEQRNG */}
        <div className="p-4 rounded-xl border shadow-sm bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
          <h3 className="text-base font-light tracking-wide mb-4 flex items-center gap-3 text-purple-600 dark:text-purple-400">
            <div className="p-1.5 rounded-lg bg-purple-50 dark:bg-purple-900/20">
              <Server size={16} className="text-purple-500" />
            </div>
            SEQRNG
          </h3>

          <div className="space-y-3">
            <div className="flex items-center gap-3">
              <Server size={14} className="text-gray-500 dark:text-gray-400" />
              <div>
                <span className="text-xs font-light tracking-wide text-gray-500 dark:text-gray-400 block">IP Address</span>
                <span className="text-sm font-light tracking-wide text-gray-900 dark:text-white">
                  {user.seqrngIpAddress || 'https://seqrng.example.com:1982'}
                </span>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <Eye size={14} className="text-gray-500 dark:text-gray-400" />
              <div>
                <span className="text-xs font-light tracking-wide text-gray-500 dark:text-gray-400 block">API Token</span>
                <span className="text-sm font-light tracking-wide text-gray-900 dark:text-white">
                  {user.seqrngApiToken || '1JcifovrZUWTeAPUeAYIZYefsY3tXFf9ZTT'}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Modal>
  );
};

UserDetailModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  user: PropTypes.object,
  darkMode: PropTypes.bool.isRequired
};

export default UserDetailModal;
