import { useState, useEffect } from 'react';

/**
 * Hook personalizado para manejar el modo oscuro
 * Centraliza la lógica de dark mode para reutilización con persistencia
 */
const useDarkMode = (initialValue = false) => {
  const [darkMode, setDarkMode] = useState(() => {
    // Leer desde localStorage al inicializar
    const saved = localStorage.getItem('darkMode');
    return saved ? JSON.parse(saved) : initialValue;
  });

  // Aplicar clase dark al HTML cuando cambie el estado
  useEffect(() => {
    if (darkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, [darkMode]);

  const toggleDarkMode = () => {
    const newDarkMode = !darkMode;
    setDarkMode(newDarkMode);

    // Guardar en localStorage para persistencia
    localStorage.setItem('darkMode', JSON.stringify(newDarkMode));
  };

  const setDarkModeValue = (value) => {
    setDarkMode(value);
    localStorage.setItem('darkMode', JSON.stringify(value));
  };

  return {
    darkMode,
    toggleDarkMode,
    setDarkMode: setDarkModeValue
  };
};

export default useDarkMode;
