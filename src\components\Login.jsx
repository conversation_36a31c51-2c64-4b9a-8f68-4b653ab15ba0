import React, { useEffect, useRef, useState } from 'react';
import gsap from 'gsap';
import { useNavigate } from 'react-router-dom';
import { Eye, EyeOff, Moon, Sun } from 'lucide-react';
import secureStorage from '../utils/SecureStorage';
import securityMonitor from '../utils/SecurityMonitor';
import securityLogger from '../utils/SecurityLogger';
import { useAuth } from '../hooks/useAuth';
import { useDarkMode } from '../hooks';

import logo1 from '../assets/sequre-logo-negro.svg';
import logo2 from '../assets/sequre-logo-negro-17.svg';
import logo3 from '../assets/sequre-logo-negro-18.svg';
import logo4 from '../assets/sequre-logo-negro-19.svg';
import logo5 from '../assets/sequre-logo-negro-20.svg';

const logoList = [logo1, logo2, logo3, logo4, logo5];

// Estilos CSS para animaciones del ojo
const eyeAnimationStyles = `
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  @keyframes scaleIn {
    from { transform: scale(0.8); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
  }

  @keyframes drawLine {
    from { stroke-dasharray: 0 100; }
    to { stroke-dasharray: 100 0; }
  }

  @keyframes eyeBlink {
    0%, 90%, 100% { transform: scaleY(1); }
    95% { transform: scaleY(0.1); }
  }
`;

const Login = () => {
  const [usuario, setUsuario] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [language, setLanguage] = useState(() => {
    // Detectar idioma del navegador automáticamente
    const browserLang = navigator.language.startsWith('es') ? 'es' : 'en';
    return localStorage.getItem('language') || browserLang;
  });
  const [currentLogoIndex, setCurrentLogoIndex] = useState(0);
  const [loginAttempts, setLoginAttempts] = useState(0);
  const [isBlocked, setIsBlocked] = useState(false);
  const [errors, setErrors] = useState({});
  const [showSuccessNotification, setShowSuccessNotification] = useState(false);
  const [showErrorNotification, setShowErrorNotification] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [isRedirecting, setIsRedirecting] = useState(false);
  const [userType, setUserType] = useState('');

  const navigate = useNavigate();
  const { login: authLogin, isLoading: authLoading } = useAuth();
  const { darkMode, toggleDarkMode } = useDarkMode();

  // Traducciones simples para el login
  const translations = {
    es: {
      welcome: '¡Bienvenido a Quantum!',
      subtitle: 'Por favor inicia sesión para continuar.',
      email: 'Email',
      password: 'Contraseña',
      login: 'Iniciar Sesión',
      loginSuccess: '¡Login Exitoso!',
      loginError: 'Error de Login',
      redirectingAdmin: 'Redirigiendo al panel de administración...',
      redirectingUser: 'Redirigiendo al panel de usuario...',
      blocked: '🚫 Bloqueado',
      loggingIn: 'Iniciando sesión...',
      blockedMessage: '🚫 Cuenta temporalmente bloqueada por seguridad. Espera 5 minutos.',
      attemptsWarning: '⚠️ Intentos fallidos: {attempts}/5. Ten cuidado con las credenciales.'
    },
    en: {
      welcome: 'Welcome to Quantum!',
      subtitle: 'Please log in to continue.',
      email: 'Email',
      password: 'Password',
      login: 'Log In',
      loginSuccess: 'Login Successful!',
      loginError: 'Login Error',
      redirectingAdmin: 'Redirecting to admin panel...',
      redirectingUser: 'Redirecting to user panel...',
      blocked: '🚫 Blocked',
      loggingIn: 'Logging in...',
      blockedMessage: '🚫 Account temporarily blocked for security. Wait 5 minutes.',
      attemptsWarning: '⚠️ Failed attempts: {attempts}/5. Be careful with credentials.'
    }
  };

  const t = (key, params = {}) => {
    let text = translations[language][key] || key;
    Object.keys(params).forEach(param => {
      text = text.replace(`{${param}}`, params[param]);
    });
    return text;
  };



  const leftPanelRef = useRef(null);
  const rightLogoRef = useRef(null);
  const mobileLogoRef = useRef(null);
  const logoImgRef = useRef(null);

///-------------------------------------------------------------------------------------------------------------------------------------------------------------------///

// 🔔 Función para mostrar notificación de error elegante como antes
const showErrorNotificationWithMessage = (message) => {
  console.log('🚨 Mostrando notificación de error elegante:', message);
  setErrorMessage(message);
  setShowErrorNotification(true);

  // Auto-ocultar después de 5 segundos
  setTimeout(() => {
    setShowErrorNotification(false);
  }, 5000);
};

// Funciones de Seguridad y Validación

// Validación robusta de email
const validateEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email) && email.length <= 254 && email.length >= 5;
};

// Validación de password
const validatePassword = (password) => {
  return password.length >= 6 && password.length <= 128;
};

// Sanitización de inputs
const sanitizeInput = (input) => {
  return input.trim().replace(/[<>'"]/g, '');
};

// Logging de seguridad (usando logger centralizado)
const logSecurityEvent = (event, details) => {
  securityLogger.logInfo(event, details);
};

// 🔐 Gestión segura de tokens con encriptación
const TokenManager = {
  setToken: (token, role) => {
    const expiry = Date.now() + (24 * 60 * 60 * 1000); // 24 horas
    const loginTime = Date.now();

    // Almacenar datos encriptados
    secureStorage.setSecure('auth_token', token);
    secureStorage.setSecure('user_role', role);
    secureStorage.setSecure('token_expiry', expiry);
    secureStorage.setSecure('login_time', loginTime);

    // También almacenar en sessionStorage como backup (sin encriptar para compatibilidad)
    sessionStorage.setItem('token', token);
    sessionStorage.setItem('userRole', role);
    sessionStorage.setItem('tokenExpiry', expiry.toString());
    sessionStorage.setItem('loginTime', loginTime.toString());

    logSecurityEvent('TOKEN_CREATED', {
      role: role,
      expiry: new Date(expiry).toISOString(),
      encrypted: true
    });
  },

  clearToken: () => {
    // Limpiar datos encriptados
    secureStorage.removeSecure('auth_token');
    secureStorage.removeSecure('user_role');
    secureStorage.removeSecure('token_expiry');
    secureStorage.removeSecure('login_time');

    // Limpiar sessionStorage
    sessionStorage.removeItem('token');
    sessionStorage.removeItem('userRole');
    sessionStorage.removeItem('tokenExpiry');
    sessionStorage.removeItem('loginTime');

    // Limpiar localStorage legacy
    localStorage.removeItem('token');
    localStorage.removeItem('userRole');

    logSecurityEvent('TOKEN_CLEARED', {
      timestamp: new Date().toISOString(),
      method: 'secure_clear'
    });
  },

  isTokenValid: () => {
    // Verificar primero datos encriptados
    const encryptedToken = secureStorage.getSecure('auth_token');
    const encryptedExpiry = secureStorage.getSecure('token_expiry');

    if (encryptedToken && encryptedExpiry) {
      if (Date.now() > encryptedExpiry) {
        TokenManager.clearToken();
        logSecurityEvent('TOKEN_EXPIRED', {
          expiry: new Date(encryptedExpiry).toISOString()
        });
        return false;
      }
      return true;
    }

    // Fallback a sessionStorage
    const token = sessionStorage.getItem('token');
    const expiry = sessionStorage.getItem('tokenExpiry');

    if (!token || !expiry) return false;

    if (Date.now() > parseInt(expiry)) {
      TokenManager.clearToken();
      return false;
    }

    return true;
  },

  getRole: () => {
    return secureStorage.getSecure('user_role') || sessionStorage.getItem('userRole');
  }
};

///-------------------------------------------------------------------------------------------------------------------------------------------------------------------///

// Animacion logo //

// Este useEffect anima la entrada de los paneles izquierdo y derecho al cargar el componente.
// Además, agrega un listener que detecta si el usuario vuelve a la pestaña (visibilidad del documento),
// y fuerza que el logo tenga opacidad 1 por si quedó invisible.

useEffect(() => {
  let index = 0;

  const cycleLogo = () => {
    index = (index + 1) % logoList.length;

    // Anima el logo: desaparece → cambia → reaparece
    gsap.to(logoImgRef.current, {
      opacity: 0,
      duration: 0.2,
      onComplete: () => {
        setCurrentLogoIndex(index);
        gsap.to(logoImgRef.current, { opacity: 1, duration: 0.2 });
      },
    });
  };

  // ⏱ Esperar 3 segundos antes de empezar a cambiar logos
  let intervalId;

  const timeoutId = setTimeout(() => {
    // Después de 3 segundos, empezar el ciclo cada 1 segundo
    intervalId = setInterval(cycleLogo, 1000);
  }, 3000);

  // 🧼 Cleanup en desmontado
  return () => {
    clearTimeout(timeoutId);
    if (intervalId) clearInterval(intervalId);
  };
}, []);

// 🎬 Animación inicial sincronizada
useEffect(() => {
  // 🎬 Timeline sincronizado para que ambos paneles aparezcan al mismo tiempo
  const tl = gsap.timeline();

  // Ambos paneles aparecen simultáneamente
  tl.fromTo(
    [leftPanelRef.current, rightLogoRef.current, mobileLogoRef.current],
    { y: 50, opacity: 0 },
    {
      y: 0,
      opacity: 1,
      duration: 1.2,
      ease: 'power2.out',
      stagger: 0 // Sin delay entre elementos - aparecen juntos
    }
  );

  // Asegura visibilidad del logo al volver de una pestaña oculta
  const handleVisibilityChange = () => {
    if (document.visibilityState === 'visible') {
      gsap.to(logoImgRef.current, { opacity: 1, duration: 0.2 });
    }
  };

  document.addEventListener('visibilitychange', handleVisibilityChange);

  return () => {
    document.removeEventListener('visibilitychange', handleVisibilityChange);
  };
}, []);

// Inicializar monitoreo de seguridad - MODO LOGIN (menos sensible)
useEffect(() => {
  // Iniciar monitoreo en modo login (menos agresivo)
  securityMonitor.startLoginMode();

  // Log de inicio de sesión de login
  logSecurityEvent('LOGIN_PAGE_ACCESSED', {
    timestamp: new Date().toISOString(),
    userAgent: navigator.userAgent,
    referrer: document.referrer,
    viewport: `${window.innerWidth}x${window.innerHeight}`,
    language: navigator.language,
    platform: navigator.platform
  });

  // Detectar si viene de logout o es primera visita
  const wasLoggedOut = sessionStorage.getItem('wasLoggedOut');
  if (wasLoggedOut) {
    logSecurityEvent('RETURN_AFTER_LOGOUT', {
      timestamp: new Date().toISOString()
    });
    sessionStorage.removeItem('wasLoggedOut');
  }

  // Limpiar al desmontar
  return () => {
    securityMonitor.stop();
  };
}, []);

///-------------------------------------------------------------------------------------------------------------------------------------------------------------------///

  // Hace un POST real al backend cuando esté listo.
  // Imprime el token que reciba (o puedes guardarlo en localStorage si lo necesitas).
  // Muestra errores claros si algo sale mal.

const handleSubmit = async (e) => {
  console.log('🚀 handleSubmit ejecutándose');
  e.preventDefault();
  console.log('🚀 preventDefault ejecutado');

  // 🚫 Prevenir envío múltiple si ya está cargando
  if (authLoading) {
    console.log('🚀 Bloqueado por authLoading');
    return;
  }

  // 🚫 Verificar si está bloqueado por rate limiting
  if (isBlocked) {
    showErrorNotificationWithMessage('⚠️ Demasiados intentos fallidos. Espera 5 minutos antes de intentar nuevamente.');
    logSecurityEvent('BLOCKED_LOGIN_ATTEMPT', {
      email: usuario.substring(0, 3) + '***',
      attempts: loginAttempts
    });
    return;
  }

  // 🧹 Limpiar errores previos
  setErrors({});

  // Sanitizar inputs
  const cleanEmail = sanitizeInput(usuario);
  const cleanPassword = sanitizeInput(password);

  // Validaciones robustas
  const newErrors = {};

  if (!cleanEmail || !cleanPassword) {
    newErrors.general = 'Todos los campos son obligatorios';
  }

  if (cleanEmail && !validateEmail(cleanEmail)) {
    newErrors.email = 'Formato de email inválido';
  }

  if (cleanPassword && !validatePassword(cleanPassword)) {
    newErrors.password = 'La contraseña debe tener entre 6 y 128 caracteres';
  }

  if (Object.keys(newErrors).length > 0) {
    setErrors(newErrors);
    logSecurityEvent('INVALID_INPUT_ATTEMPT', {
      email: cleanEmail.substring(0, 3) + '***',
      errors: Object.keys(newErrors)
    });

    // Mostrar notificación para errores de validación
    const errorMessages = Object.values(newErrors);
    showErrorNotificationWithMessage(`⚠️ ${errorMessages[0]}`);

    return;
  }

  try {
    // 🌐 Llamada directa al backend SQQ
    const response = await authLogin(cleanEmail, cleanPassword);

    // Login exitoso
    setLoginAttempts(0); // Reset intentos

    // Determinar tipo de usuario y ruta
    const userRole = response.user.role;
    const isAdmin = userRole === 'ADMIN' || userRole === 'admin';

    // Normalizar rol para compatibilidad con sistema legacy
    const normalizedRole = isAdmin ? 'admin' : 'usuario';

    // Role normalization completed

    // Actualizar sessionStorage con rol normalizado para compatibilidad
    sessionStorage.setItem('userRole', normalizedRole);

    setUserType(normalizedRole);
    setShowSuccessNotification(true);
    setIsRedirecting(true);

    // Cambiar SecurityMonitor a modo normal después de login exitoso
    securityMonitor.switchToNormalMode();

    setTimeout(() => {
      navigate(isAdmin ? '/admin' : '/usuario');
    }, 2000); // Redirigir después de 2 segundos

  } catch (error) {
    // Error de login
    console.error('Error en login:', error);
    handleLoginFailure(cleanEmail, error.message || 'Error de conexión con el servidor');
  }
};

// Manejo de fallos de login con rate limiting
const handleLoginFailure = (email, errorMessage) => {
  const newAttempts = loginAttempts + 1;
  setLoginAttempts(newAttempts);

  logSecurityEvent('FAILED_LOGIN_ATTEMPT', {
    email: email.substring(0, 3) + '***',
    attempt: newAttempts,
    error: errorMessage,
    ip: 'frontend-log' // En producción, el backend capturaría la IP real
  });

  if (newAttempts >= 5) {
    setIsBlocked(true);
    logSecurityEvent('USER_BLOCKED', {
      email: email.substring(0, 3) + '***',
      totalAttempts: newAttempts
    });

    // Desbloquear después de 5 minutos
    setTimeout(() => {
      setIsBlocked(false);
      setLoginAttempts(0);
      logSecurityEvent('USER_UNBLOCKED', {
        email: email.substring(0, 3) + '***'
      });
    }, 5 * 60 * 1000); // 5 minutos

    showErrorNotificationWithMessage('🚫 Demasiados intentos fallidos. Cuenta bloqueada por 5 minutos.');
  } else {
    showErrorNotificationWithMessage(`⚠️ Cuidado, te quedan ${5 - newAttempts} intentos restantes`);
  }
};




///-------------------------------------------------------------------------------------------------------------------------------------------------------------------///
  // Renderiza el componente de inicio de sesión

  // con un formulario de usuario y contraseña, y un logo que cambia cada segundo.
  // El logo cambia con una animación suave y los paneles tienen una entrada animada.
  // El panel izquierdo contiene el formulario de inicio de sesión y el derecho muestra el logo.
  // El logo cambia cada segundo con una animación de desvanecimiento.
  // El formulario tiene campos para usuario y contraseña, y un botón para enviar.
  // El componente utiliza gsap para las animaciones y tiene un estado para manejar el usuario, contraseña y el índice del logo actual.
  return (
    <div className={`min-h-screen w-full flex flex-col lg:flex-row transition-colors duration-500 ${
      darkMode ? 'bg-gray-800' : 'bg-white'
    }`}>
      {/* Estilos CSS para animaciones del ojo */}
      <style>{eyeAnimationStyles}</style>

      {/* Botón de modo oscuro - Arriba izquierda */}
      <button
        onClick={toggleDarkMode}
        className="absolute top-4 left-4 z-50 p-3 rounded-xl transition-all duration-300 hover:scale-110 active:scale-95 group"
        style={{
          backgroundColor: darkMode ? 'rgba(55, 65, 81, 0.9)' : 'rgba(255, 255, 255, 0.9)',
          backdropFilter: 'blur(10px)',
          border: darkMode ? '1px solid rgba(75, 85, 99, 0.3)' : '1px solid rgba(229, 231, 235, 0.3)',
          boxShadow: darkMode
            ? '0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2)'
            : '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
        }}
      >
        {darkMode ? (
          <Sun
            size={20}
            className="text-yellow-400 group-hover:text-yellow-300 transition-all duration-300 group-hover:rotate-12 transform"
          />
        ) : (
          <Moon
            size={20}
            className="text-gray-600 group-hover:text-indigo-600 transition-all duration-300 group-hover:-rotate-12 transform"
          />
        )}
      </button>

      {/* Panel izquierdo - Formulario */}
      <div
        ref={leftPanelRef}
        className="w-full lg:w-1/2 flex flex-col justify-center items-center p-6 sm:p-8 lg:p-10 opacity-0"
      >
        <img
          src={logo1}
          alt="Logo"
          className={`mb-4 sm:mb-6 w-32 sm:w-40 transition-all duration-500 ${
            darkMode ? 'filter brightness-0 invert' : ''
          }`}
        />
        <h2 className={`text-lg sm:text-xl font-semibold mb-2 text-center transition-colors duration-500 ${
          darkMode ? 'text-white' : 'text-gray-800'
        }`}>
          {t('welcome')}
        </h2>
        <p className={`mb-4 sm:mb-6 text-center text-sm sm:text-base transition-colors duration-500 ${
          darkMode ? 'text-gray-300' : 'text-gray-600'
        }`}>
          {t('subtitle')}
        </p>

        {/* 🚨 Mostrar errores de validación */}
        {errors.general && (
          <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded-lg text-xs sm:text-sm w-full max-w-sm">
            {errors.general}
          </div>
        )}

        {/* ⚠️ Mostrar estado de bloqueo */}
        {isBlocked && (
          <div className="mb-4 p-3 bg-yellow-100 border border-yellow-400 text-yellow-700 rounded-lg text-xs sm:text-sm w-full max-w-sm">
            🚫 Cuenta temporalmente bloqueada por seguridad. Espera 5 minutos.
          </div>
        )}

        {/* 📊 Mostrar intentos restantes */}
        {loginAttempts > 0 && !isBlocked && (
          <div className="mb-4 p-3 bg-orange-100 border border-orange-400 text-orange-700 rounded-lg text-xs sm:text-sm w-full max-w-sm">
            ⚠️ Intentos fallidos: {loginAttempts}/5. Ten cuidado con las credenciales.
          </div>
        )}

        <form onSubmit={handleSubmit} className="w-full max-w-sm px-4 sm:px-0" autoComplete="off">
          <input
            type="email"
            placeholder={t('email')}
            value={usuario}
            onChange={(e) => setUsuario(e.target.value)}
            className={`w-full px-4 py-3 mb-2 border rounded-xl focus:outline-none focus:ring-2 transition-all duration-300 text-sm sm:text-base ${
              errors.email
                ? 'border-red-400 focus:ring-red-400'
                : darkMode
                  ? 'border-gray-600 focus:ring-indigo-400 bg-gray-800 text-white placeholder-gray-400'
                  : 'border-gray-300 focus:ring-indigo-400 bg-white text-gray-900 placeholder-gray-500'
            }`}
            maxLength="254"
            required
            disabled={isBlocked}
          />
          {errors.email && (
            <p className="text-red-600 text-xs mb-2">{errors.email}</p>
          )}

          <div className="relative mb-2">
            <input
              type={showPassword ? "text" : "password"}
              placeholder={t('password')}
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className={`w-full px-4 py-3 pr-14 border rounded-xl focus:outline-none focus:ring-2 transition-all duration-300 text-sm sm:text-base ${
                errors.password
                  ? 'border-red-400 focus:ring-red-400'
                  : darkMode
                    ? 'border-gray-600 focus:ring-indigo-400 bg-gray-800 text-white placeholder-gray-400'
                    : 'border-gray-300 focus:ring-indigo-400 bg-white text-gray-900 placeholder-gray-500'
              }`}
              maxLength="128"
              minLength="6"
              required
              disabled={isBlocked}
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 p-1 transition-all duration-300 hover:scale-110 active:scale-95 group"
              disabled={isBlocked}
              tabIndex={-1}
              style={{ marginTop: '-1px' }}
            >
              <div className="relative">
                <div className={`transition-all duration-500 ease-in-out ${showPassword ? 'animate-pulse' : ''}`}>
                  {showPassword ? (
                    <div className="relative">
                      {/* Ojo abierto con animación */}
                      <svg
                        width="22"
                        height="22"
                        viewBox="0 0 24 24"
                        fill="none"
                        className="text-gray-500 group-hover:text-indigo-600 transition-all duration-300 transform group-hover:scale-105"
                      >
                        <path
                          d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5z"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="animate-[fadeIn_0.3s_ease-in-out]"
                        />
                        <circle
                          cx="12"
                          cy="12"
                          r="3"
                          stroke="currentColor"
                          strokeWidth="2"
                          className="animate-[scaleIn_0.4s_ease-in-out]"
                        />
                      </svg>
                    </div>
                  ) : (
                    <div className="relative">
                      {/* Ojo cerrado con animación */}
                      <svg
                        width="22"
                        height="22"
                        viewBox="0 0 24 24"
                        fill="none"
                        className="text-gray-500 group-hover:text-indigo-600 transition-all duration-300 transform group-hover:scale-105"
                      >
                        <path
                          d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="animate-[fadeIn_0.3s_ease-in-out]"
                        />
                        <path
                          d="M1 1l22 22"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="animate-[drawLine_0.5s_ease-in-out]"
                        />
                      </svg>
                    </div>
                  )}
                </div>
              </div>
            </button>
          </div>
          {errors.password && (
            <p className="text-red-600 text-xs mb-2">{errors.password}</p>
          )}
          <button
            type="submit"
            disabled={isBlocked || authLoading}
            className={`w-full flex items-center justify-center gap-3 py-3 px-6 rounded-xl font-light tracking-wide shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 group ${
              isBlocked || authLoading
                ? 'bg-gray-400 text-gray-600 cursor-not-allowed'
                : 'bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white'
            }`}
          >
            {!isBlocked && !authLoading && (
              <div className="p-1 bg-white/20 rounded-lg group-hover:bg-white/30 transition-all duration-300">
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M3 3a1 1 0 011 1v12a1 1 0 11-2 0V4a1 1 0 011-1zm7.707 3.293a1 1 0 010 1.414L9.414 9H17a1 1 0 110 2H9.414l1.293 1.293a1 1 0 01-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </div>
            )}
            {authLoading && (
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
            )}
            <span className="font-light tracking-wide">
              {isBlocked ? t('blocked') : authLoading ? t('loggingIn') : t('login')}
            </span>
          </button>
        </form>
      </div>

      {/* Panel derecho - Logos cambiando (Desktop) */}
      <div
        ref={rightLogoRef}
        className="hidden lg:flex w-full lg:w-1/2 flex-col justify-center items-center p-6 sm:p-8 lg:p-10 opacity-0"
      >
        <img
          ref={logoImgRef}
          src={logoList[currentLogoIndex]}
          alt="Logo Quantum"
          className={`w-2/3 max-w-md opacity-100 transition-all duration-500 ${
            darkMode ? 'filter brightness-0 invert' : ''
          }`}
        />
      </div>

      {/* Panel móvil - Logos cambiando (Mobile/Tablet) */}
      <div
        ref={mobileLogoRef}
        className="flex lg:hidden w-full flex-col justify-center items-center p-6 sm:p-8 opacity-0"
      >
        <img
          src={logoList[currentLogoIndex]}
          alt="Logo Quantum"
          className={`w-3/4 sm:w-2/3 max-w-xs opacity-100 transition-all duration-500 ${
            darkMode ? 'filter brightness-0 invert' : ''
          }`}
        />
      </div>

      {/* Notificaciones ahora manejadas por React Hot Toast */}

      {/* 🎉 Notificación de éxito elegante como antes */}
      {showSuccessNotification && (
        <div className="fixed bottom-4 sm:bottom-8 left-1/2 transform -translate-x-1/2 z-50 px-4">
          <div className="bg-green-500 text-white px-4 sm:px-8 py-3 sm:py-4 rounded-lg shadow-2xl flex items-center gap-3 animate-bounce max-w-sm sm:max-w-none">
            <div className="w-5 h-5 sm:w-6 sm:h-6 bg-white rounded-full flex items-center justify-center flex-shrink-0">
              <span className="text-green-500 font-bold text-sm sm:text-lg">✓</span>
            </div>
            <div className="min-w-0">
              <p className="font-bold text-sm sm:text-lg">{t('loginSuccess')}</p>
              <p className="text-xs sm:text-sm opacity-90 truncate">
                {isRedirecting
                  ? (userType === 'admin'
                      ? t('redirectingAdmin')
                      : t('redirectingUser'))
                  : (userType === 'admin'
                      ? 'Bienvenido Admin Quantum'
                      : 'Bienvenido Usuario Quantum')
                }
              </p>
            </div>
            {isRedirecting && (
              <div className="ml-2 sm:ml-4 flex-shrink-0">
                <div className="w-4 h-4 sm:w-5 sm:h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* 🚨 Notificación de error elegante como antes */}
      {showErrorNotification && (
        <div className="fixed bottom-4 sm:bottom-8 left-1/2 transform -translate-x-1/2 z-50 px-4">
          <div className="bg-red-500 text-white px-4 sm:px-8 py-3 sm:py-4 rounded-lg shadow-2xl flex items-center gap-3 animate-bounce max-w-sm sm:max-w-md">
            <div className="w-5 h-5 sm:w-6 sm:h-6 bg-white rounded-full flex items-center justify-center flex-shrink-0">
              <span className="text-red-500 font-bold text-sm sm:text-lg">✗</span>
            </div>
            <div className="min-w-0">
              <p className="font-bold text-sm sm:text-lg">{t('loginError')}</p>
              <p className="text-xs sm:text-sm opacity-90 break-words">
                {errorMessage}
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Login;
