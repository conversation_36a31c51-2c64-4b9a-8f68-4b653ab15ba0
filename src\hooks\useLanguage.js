import { useTranslation } from 'react-i18next';

/**
 * Hook personalizado para manejo de idiomas
 * Proporciona funciones para cambiar idioma y obtener traducciones
 */
export const useLanguage = () => {
  const { t, i18n } = useTranslation();

  // Idiomas disponibles
  const languages = [
    {
      code: 'es',
      name: 'Español',
      flag: '🇪🇸',
      nativeName: 'Español'
    },
    {
      code: 'en',
      name: 'English',
      flag: '🇺🇸',
      nativeName: 'English'
    }
  ];

  // Obtener idioma actual
  const currentLanguage = i18n.language || 'es';

  // Obtener información del idioma actual
  const getCurrentLanguageInfo = () => {
    return languages.find(lang => lang.code === currentLanguage) || languages[0];
  };

  // Cambiar idioma - SIN RECARGA
  const changeLanguage = async (languageCode) => {
    try {
      await i18n.changeLanguage(languageCode);
      localStorage.setItem('i18nextLng', languageCode);
      return true;
    } catch (error) {
      console.error('Error changing language:', error);
      return false;
    }
  };

  // Verificar si un idioma está disponible
  const isLanguageAvailable = (languageCode) => {
    return languages.some(lang => lang.code === languageCode);
  };

  // Detectar idioma del navegador
  const getBrowserLanguage = () => {
    const browserLang = navigator.language || navigator.languages[0];
    const langCode = browserLang.split('-')[0];
    return isLanguageAvailable(langCode) ? langCode : 'es';
  };

  return {
    // Función de traducción
    t,

    // Información de idiomas
    languages,
    currentLanguage,
    getCurrentLanguageInfo,

    // Funciones de control
    changeLanguage,
    isLanguageAvailable,
    getBrowserLanguage,

    // Estado
    isReady: i18n.isInitialized
  };
};

export default useLanguage;
