{"name": "quantum-login", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:prod": "vite build --mode production", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"gsap": "^3.13.0", "i18next": "^25.3.2", "i18next-browser-languagedetector": "^8.2.0", "lucide-react": "^0.525.0", "prop-types": "^15.8.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-i18next": "^15.6.0", "react-router-dom": "^7.6.3"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "autoprefixer": "^10.4.21", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "postcss": "^8.5.6", "tailwindcss": "^3.3.5", "terser": "^5.43.1", "vite": "^7.0.0"}}